[{"C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Home.js": "4", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Login.js": "5", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\DefectDetail.js": "6", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Pavement.js": "7", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Recommendation.js": "8", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Dashboard.js": "9", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js": "10", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\Sidebar.js": "11", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\Header.js": "12", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js": "13", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\ChartContainer.js": "14", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js": "15", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\DefectMap.js": "16", "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\hooks\\useResponsive.js": "17"}, {"size": 633, "mtime": 1752580134747, "results": "18", "hashOfConfig": "19"}, {"size": 376, "mtime": 1752580134760, "results": "20", "hashOfConfig": "19"}, {"size": 2973, "mtime": 1752580134735, "results": "21", "hashOfConfig": "19"}, {"size": 3312, "mtime": 1752580134751, "results": "22", "hashOfConfig": "19"}, {"size": 4855, "mtime": 1752580134753, "results": "23", "hashOfConfig": "19"}, {"size": 15911, "mtime": 1752736368087, "results": "24", "hashOfConfig": "19"}, {"size": 62133, "mtime": 1752736332264, "results": "25", "hashOfConfig": "19"}, {"size": 26623, "mtime": 1752580134756, "results": "26", "hashOfConfig": "19"}, {"size": 30881, "mtime": 1752580134748, "results": "27", "hashOfConfig": "19"}, {"size": 36764, "mtime": 1752580134758, "results": "28", "hashOfConfig": "19"}, {"size": 4491, "mtime": 1752580134743, "results": "29", "hashOfConfig": "19"}, {"size": 659, "mtime": 1752580134740, "results": "30", "hashOfConfig": "19"}, {"size": 4005, "mtime": 1752580134741, "results": "31", "hashOfConfig": "19"}, {"size": 3414, "mtime": 1752580134739, "results": "32", "hashOfConfig": "19"}, {"size": 33593, "mtime": 1752736271170, "results": "33", "hashOfConfig": "19"}, {"size": 14715, "mtime": 1752580134740, "results": "34", "hashOfConfig": "19"}, {"size": 1018, "mtime": 1752580134746, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "sqm6hm", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Login.js", ["87"], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\DefectDetail.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Pavement.js", ["88", "89", "90", "91", "92", "93", "94"], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Recommendation.js", ["95", "96", "97", "98", "99", "100", "101", "102", "103", "104"], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\Dashboard.js", ["105", "106"], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js", ["107", "108", "109", "110", "111", "112", "113"], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\Sidebar.js", ["114", "115"], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js", ["116"], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\ChartContainer.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js", ["117", "118", "119"], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\components\\DefectMap.js", ["120", "121", "122"], [], "C:\\Users\\<USER>\\Deep Learning\\LTA_GIT\\LTA\\frontend\\src\\hooks\\useResponsive.js", [], [], {"ruleId": "123", "severity": 1, "message": "124", "line": 52, "column": 29, "nodeType": "125", "messageId": "126", "endLine": 52, "endColumn": 78}, {"ruleId": "127", "severity": 1, "message": "128", "line": 38, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 38, "endColumn": 30}, {"ruleId": "127", "severity": 1, "message": "131", "line": 39, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 39, "endColumn": 29}, {"ruleId": "127", "severity": 1, "message": "132", "line": 493, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 493, "endColumn": 28}, {"ruleId": "133", "severity": 1, "message": "134", "line": 559, "column": 6, "nodeType": "135", "endLine": 559, "endColumn": 20, "suggestions": "136"}, {"ruleId": "133", "severity": 1, "message": "137", "line": 596, "column": 6, "nodeType": "135", "endLine": 596, "endColumn": 33, "suggestions": "138"}, {"ruleId": "127", "severity": 1, "message": "139", "line": 605, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 605, "endColumn": 27}, {"ruleId": "140", "severity": 1, "message": "141", "line": 648, "column": 21, "nodeType": "142", "endLine": 652, "endColumn": 23}, {"ruleId": "127", "severity": 1, "message": "143", "line": 3, "column": 58, "nodeType": "129", "messageId": "130", "endLine": 3, "endColumn": 63}, {"ruleId": "127", "severity": 1, "message": "144", "line": 3, "column": 65, "nodeType": "129", "messageId": "130", "endLine": 3, "endColumn": 69}, {"ruleId": "127", "severity": 1, "message": "145", "line": 3, "column": 71, "nodeType": "129", "messageId": "130", "endLine": 3, "endColumn": 74}, {"ruleId": "127", "severity": 1, "message": "146", "line": 4, "column": 8, "nodeType": "129", "messageId": "130", "endLine": 4, "endColumn": 12}, {"ruleId": "127", "severity": 1, "message": "147", "line": 76, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 76, "endColumn": 25}, {"ruleId": "127", "severity": 1, "message": "148", "line": 79, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 79, "endColumn": 19}, {"ruleId": "127", "severity": 1, "message": "149", "line": 277, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 277, "endColumn": 22}, {"ruleId": "127", "severity": 1, "message": "150", "line": 291, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 291, "endColumn": 21}, {"ruleId": "127", "severity": 1, "message": "151", "line": 305, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 305, "endColumn": 30}, {"ruleId": "127", "severity": 1, "message": "152", "line": 318, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 318, "endColumn": 28}, {"ruleId": "127", "severity": 1, "message": "146", "line": 4, "column": 8, "nodeType": "129", "messageId": "130", "endLine": 4, "endColumn": 12}, {"ruleId": "133", "severity": 1, "message": "153", "line": 295, "column": 6, "nodeType": "135", "endLine": 295, "endColumn": 8, "suggestions": "154"}, {"ruleId": "127", "severity": 1, "message": "155", "line": 4, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 4, "endColumn": 22}, {"ruleId": "127", "severity": 1, "message": "156", "line": 4, "column": 24, "nodeType": "129", "messageId": "130", "endLine": 4, "endColumn": 33}, {"ruleId": "127", "severity": 1, "message": "157", "line": 4, "column": 35, "nodeType": "129", "messageId": "130", "endLine": 4, "endColumn": 41}, {"ruleId": "127", "severity": 1, "message": "158", "line": 4, "column": 43, "nodeType": "129", "messageId": "130", "endLine": 4, "endColumn": 48}, {"ruleId": "127", "severity": 1, "message": "159", "line": 23, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 23, "endColumn": 19}, {"ruleId": "127", "severity": 1, "message": "160", "line": 70, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 70, "endColumn": 25}, {"ruleId": "140", "severity": 1, "message": "141", "line": 536, "column": 23, "nodeType": "142", "endLine": 540, "endColumn": 25}, {"ruleId": "127", "severity": 1, "message": "161", "line": 18, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 18, "endColumn": 20}, {"ruleId": "162", "severity": 1, "message": "163", "line": 88, "column": 11, "nodeType": "142", "endLine": 88, "endColumn": 15}, {"ruleId": "127", "severity": 1, "message": "164", "line": 35, "column": 11, "nodeType": "129", "messageId": "130", "endLine": 35, "endColumn": 16}, {"ruleId": "127", "severity": 1, "message": "165", "line": 16, "column": 10, "nodeType": "129", "messageId": "130", "endLine": 16, "endColumn": 20}, {"ruleId": "127", "severity": 1, "message": "166", "line": 40, "column": 9, "nodeType": "129", "messageId": "130", "endLine": 40, "endColumn": 20}, {"ruleId": "133", "severity": 1, "message": "167", "line": 91, "column": 6, "nodeType": "135", "endLine": 91, "endColumn": 19, "suggestions": "168"}, {"ruleId": "127", "severity": 1, "message": "169", "line": 42, "column": 18, "nodeType": "129", "messageId": "130", "endLine": 42, "endColumn": 27}, {"ruleId": "127", "severity": 1, "message": "170", "line": 43, "column": 16, "nodeType": "129", "messageId": "130", "endLine": 43, "endColumn": 23}, {"ruleId": "133", "severity": 1, "message": "171", "line": 141, "column": 6, "nodeType": "135", "endLine": 141, "endColumn": 12, "suggestions": "172"}, "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'retryCount'.", "ArrowFunctionExpression", "unsafeRefs", "no-unused-vars", "'autoNavigationActive' is assigned a value but never used.", "Identifier", "unusedVar", "'autoNavigationIndex' is assigned a value but never used.", "'startAutoNavigation' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'handleLocationRequest' and 'locationPermission'. Either include them or remove the dependency array.", "ArrayExpression", ["173"], "React Hook useEffect has a missing dependency: 'handleLocationRequest'. Either include it or remove the dependency array.", ["174"], "'stopAutoNavigation' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Badge' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Plot' is defined but never used.", "'recommendations' is assigned a value but never used.", "'chartData' is assigned a value but never used.", "'handleApprove' is assigned a value but never used.", "'handleReject' is assigned a value but never used.", "'getPriorityBadgeClass' is assigned a value but never used.", "'getStatusBadgeClass' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'endDate', 'fetchData', and 'startDate'. Either include them or remove the dependency array.", ["175"], "'MapContainer' is defined but never used.", "'TileLayer' is defined but never used.", "'Marker' is defined but never used.", "'Popup' is defined but never used.", "'imageFile' is assigned a value but never used.", "'roadInfraClasses' is assigned a value but never used.", "'activePage' is assigned a value but never used.", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'value' is assigned a value but never used.", "'shouldStop' is assigned a value but never used.", "'BUFFER_SIZE' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleStopRecording'. Either include it or remove the dependency array.", ["176"], "'setCenter' is assigned a value but never used.", "'setZoom' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchDefectData' and 'fetchUsers'. Either include them or remove the dependency array.", ["177"], {"desc": "178", "fix": "179"}, {"desc": "180", "fix": "181"}, {"desc": "182", "fix": "183"}, {"desc": "184", "fix": "185"}, {"desc": "186", "fix": "187"}, "Update the dependencies array to be: [cameraActive, handleLocationRequest, locationPermission]", {"range": "188", "text": "189"}, "Update the dependencies array to be: [cameraActive, coordinates, handleLocationRequest]", {"range": "190", "text": "191"}, "Update the dependencies array to be: [endDate, fetchData, startDate]", {"range": "192", "text": "193"}, "Update the dependencies array to be: [handleStopRecording, isRecording]", {"range": "194", "text": "195"}, "Update the dependencies array to be: [fetchDefectData, fetchUsers, user]", {"range": "196", "text": "197"}, [19306, 19320], "[cameraActive, handleLocationRequest, locationPermission]", [20527, 20554], "[cameraActive, coordinates, handleLocationRequest]", [10553, 10555], "[endDate, fetchData, startDate]", [3269, 3282], "[handleStopRecording, isRecording]", [5311, 5317], "[fetchDefectData, fetchUsers, user]"]