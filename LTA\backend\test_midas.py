#!/usr/bin/env python3
"""
Test script to verify MiDaS depth estimation functionality
"""

import numpy as np
import cv2
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.models import load_midas, estimate_depth

def test_midas():
    """Test MiDaS depth estimation"""
    
    print("🌊 MIDAS TEST: Loading MiDaS model...")
    
    try:
        midas, midas_transform = load_midas()
        print(f"✅ MIDAS TEST: Model loaded successfully")
        print(f"📊 MIDAS TEST: midas type: {type(midas)}")
        print(f"📊 MIDAS TEST: transform type: {type(midas_transform)}")
        
        # Create a test image (simple gradient)
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Create a gradient pattern (simulates depth variation)
        for i in range(480):
            test_image[i, :, :] = int(255 * i / 480)  # Vertical gradient
        
        # Add some objects (rectangles) at different "depths"
        cv2.rectangle(test_image, (100, 100), (200, 200), (255, 255, 255), -1)  # White rectangle
        cv2.rectangle(test_image, (300, 200), (400, 300), (128, 128, 128), -1)  # Gray rectangle
        cv2.rectangle(test_image, (450, 300), (550, 400), (64, 64, 64), -1)    # Dark rectangle
        
        print(f"📊 MIDAS TEST: Test image shape: {test_image.shape}")
        print(f"📊 MIDAS TEST: Test image range: [{np.min(test_image)}, {np.max(test_image)}]")
        
        # Test depth estimation
        print(f"🌊 MIDAS TEST: Running depth estimation...")
        depth_map = estimate_depth(test_image, midas, midas_transform)
        
        if depth_map is not None:
            print(f"✅ MIDAS TEST: Depth estimation successful!")
            print(f"📊 MIDAS TEST: Depth map shape: {depth_map.shape}")
            print(f"📊 MIDAS TEST: Depth map range: [{np.min(depth_map)}, {np.max(depth_map)}]")
            print(f"📊 MIDAS TEST: Depth map dtype: {depth_map.dtype}")
            print(f"📊 MIDAS TEST: Depth map unique values count: {len(np.unique(depth_map))}")
            
            # Check if depth map has variation
            depth_std = np.std(depth_map)
            print(f"📊 MIDAS TEST: Depth map standard deviation: {depth_std:.2f}")
            
            if depth_std > 5:
                print(f"✅ MIDAS TEST: Depth map has good variation!")
            else:
                print(f"⚠️ MIDAS TEST: Depth map has low variation, may cause issues")
                
            # Save test images for inspection
            cv2.imwrite('test_input_image.jpg', test_image)
            cv2.imwrite('test_depth_map.jpg', depth_map)
            print(f"💾 MIDAS TEST: Saved test_input_image.jpg and test_depth_map.jpg")
            
        else:
            print(f"❌ MIDAS TEST: Depth estimation failed!")
            
    except Exception as e:
        print(f"❌ MIDAS TEST: Error loading MiDaS: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_midas()
