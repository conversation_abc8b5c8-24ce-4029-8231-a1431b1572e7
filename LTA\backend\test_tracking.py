#!/usr/bin/env python3
"""
Test script to verify the tracking system implementation
"""

import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routes.pavement import DefectTracker

def test_tracking_system():
    """Test the tracking system with simulated detections"""
    
    print("🧪 TRACKING TEST: Testing DefectTracker implementation")
    
    # Initialize tracker
    tracker = DefectTracker(max_missing_frames=5, confidence_threshold=0.3)
    
    # Simulate frame 1 - 3 new potholes detected
    frame1_detections = [
        {
            'type': 'Pothole',
            'bbox': [100, 100, 200, 200],
            'confidence': 0.8,
            'area_cm2': 150.0,
            'volume': 1500.0,
            'depth_cm': 10.0,
            'volume_range': 'Medium'
        },
        {
            'type': 'Pothole',
            'bbox': [300, 150, 400, 250],
            'confidence': 0.7,
            'area_cm2': 200.0,
            'volume': 2000.0,
            'depth_cm': 12.0,
            'volume_range': 'Medium'
        },
        {
            'type': 'Pothole',
            'bbox': [500, 200, 600, 300],
            'confidence': 0.9,
            'area_cm2': 100.0,
            'volume': 800.0,
            'depth_cm': 8.0,
            'volume_range': 'Small'
        }
    ]
    
    print(f"\n📊 FRAME 1: Processing {len(frame1_detections)} detections")
    tracked_frame1 = tracker.update(frame1_detections, frame_count=1)
    stats1 = tracker.get_tracking_stats()
    
    print(f"   - Tracked detections: {len(tracked_frame1)}")
    print(f"   - Track IDs assigned: {[d.get('track_id') for d in tracked_frame1]}")
    print(f"   - Unique detections: {stats1['total_unique_detections']}")
    print(f"   - Next ID: {stats1['next_id']}")
    
    # Simulate frame 2 - same potholes moved slightly + 1 new pothole
    frame2_detections = [
        {
            'type': 'Pothole',
            'bbox': [105, 105, 205, 205],  # Pothole 1 moved slightly
            'confidence': 0.8,
            'area_cm2': 155.0,
            'volume': 1550.0,
            'depth_cm': 10.5,
            'volume_range': 'Medium'
        },
        {
            'type': 'Pothole',
            'bbox': [305, 155, 405, 255],  # Pothole 2 moved slightly
            'confidence': 0.75,
            'area_cm2': 205.0,
            'volume': 2100.0,
            'depth_cm': 12.5,
            'volume_range': 'Medium'
        },
        {
            'type': 'Pothole',
            'bbox': [700, 100, 800, 200],  # New pothole
            'confidence': 0.6,
            'area_cm2': 120.0,
            'volume': 900.0,
            'depth_cm': 7.5,
            'volume_range': 'Small'
        }
    ]
    
    print(f"\n📊 FRAME 2: Processing {len(frame2_detections)} detections")
    tracked_frame2 = tracker.update(frame2_detections, frame_count=2)
    stats2 = tracker.get_tracking_stats()
    
    print(f"   - Tracked detections: {len(tracked_frame2)}")
    print(f"   - Track IDs assigned: {[d.get('track_id') for d in tracked_frame2]}")
    print(f"   - Unique detections: {stats2['total_unique_detections']}")
    print(f"   - Next ID: {stats2['next_id']}")
    
    # Simulate frame 10 - only 2 potholes visible (one disappeared)
    frame10_detections = [
        {
            'type': 'Pothole',
            'bbox': [110, 110, 210, 210],  # Pothole 1 still visible
            'confidence': 0.8,
            'area_cm2': 160.0,
            'volume': 1600.0,
            'depth_cm': 11.0,
            'volume_range': 'Medium'
        },
        {
            'type': 'Pothole',
            'bbox': [705, 105, 805, 205],  # New pothole still visible
            'confidence': 0.65,
            'area_cm2': 125.0,
            'volume': 950.0,
            'depth_cm': 8.0,
            'volume_range': 'Small'
        }
    ]
    
    print(f"\n📊 FRAME 10: Processing {len(frame10_detections)} detections")
    tracked_frame10 = tracker.update(frame10_detections, frame_count=10)
    stats10 = tracker.get_tracking_stats()
    
    print(f"   - Tracked detections: {len(tracked_frame10)}")
    print(f"   - Track IDs assigned: {[d.get('track_id') for d in tracked_frame10]}")
    print(f"   - Unique detections: {stats10['total_unique_detections']}")
    print(f"   - Active tracks: {stats10['active_tracks']}")
    print(f"   - Next ID: {stats10['next_id']}")
    
    # Get final unique detections
    unique_detections = tracker.get_unique_detections()
    
    print(f"\n✅ FINAL RESULTS:")
    print(f"   - Total unique detections: {len(unique_detections)}")
    print(f"   - Sequential IDs: {[d.get('track_id') for d in unique_detections]}")
    print(f"   - Original track IDs: {[d.get('original_track_id') for d in unique_detections]}")
    
    # Verify sequential numbering
    expected_ids = list(range(1, len(unique_detections) + 1))
    actual_ids = [d.get('track_id') for d in unique_detections]
    
    if actual_ids == expected_ids:
        print(f"✅ SUCCESS: Track IDs are properly sequential: {actual_ids}")
    else:
        print(f"❌ FAILURE: Track IDs are not sequential")
        print(f"   Expected: {expected_ids}")
        print(f"   Actual: {actual_ids}")
    
    return unique_detections

if __name__ == "__main__":
    test_tracking_system()
