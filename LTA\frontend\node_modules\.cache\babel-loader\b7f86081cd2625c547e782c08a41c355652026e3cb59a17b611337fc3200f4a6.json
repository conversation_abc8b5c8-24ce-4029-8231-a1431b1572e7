{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTA_GIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\VideoDefectDetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Card, Button, Form, Alert, Spinner, Table, Row, Col } from 'react-bootstrap';\nimport axios from 'axios';\nimport Webcam from 'react-webcam';\nimport useResponsive from '../hooks/useResponsive';\nimport './VideoDefectDetection.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoDefectDetection = () => {\n  _s();\n  const [selectedModel, setSelectedModel] = useState('All');\n  const [videoFile, setVideoFile] = useState(null);\n  const [videoPreview, setVideoPreview] = useState(null);\n  const [processedVideo, setProcessedVideo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [shouldStop, setShouldStop] = useState(false);\n  const [coordinates, setCoordinates] = useState('Not Available');\n  const [inputSource, setInputSource] = useState('video');\n  const [cameraActive, setCameraActive] = useState(false);\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [recordedChunks, setRecordedChunks] = useState([]);\n\n  // Video processing states\n  const [frameBuffer, setFrameBuffer] = useState([]);\n  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);\n  const [isBuffering, setIsBuffering] = useState(false);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [processingProgress, setProcessingProgress] = useState(0);\n  const [allDetections, setAllDetections] = useState([]);\n  const [videoResults, setVideoResults] = useState(null);\n  const webcamRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const recordingTimerRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const {\n    isMobile\n  } = useResponsive();\n  const BUFFER_SIZE = 10;\n  const PLAYBACK_FPS = 15;\n  const MAX_RECORDING_TIME = 60; // 1 minute limit\n\n  // Available models\n  const modelOptions = [{\n    value: 'All',\n    label: 'All (detect all types of defects)'\n  }, {\n    value: 'Potholes',\n    label: 'Potholes'\n  }, {\n    value: 'Alligator Cracks',\n    label: 'Alligator Cracks'\n  }, {\n    value: 'Kerbs',\n    label: 'Kerbs'\n  }];\n\n  // Get user location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        const {\n          latitude,\n          longitude\n        } = position.coords;\n        setCoordinates(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);\n      }, err => {\n        console.error(\"Error getting location:\", err);\n        setCoordinates('Location unavailable');\n      });\n    }\n  }, []);\n\n  // Recording timer\n  useEffect(() => {\n    if (isRecording) {\n      recordingTimerRef.current = setInterval(() => {\n        setRecordingTime(prev => {\n          if (prev >= MAX_RECORDING_TIME) {\n            handleStopRecording();\n            return MAX_RECORDING_TIME;\n          }\n          return prev + 1;\n        });\n      }, 1000);\n    } else {\n      if (recordingTimerRef.current) {\n        clearInterval(recordingTimerRef.current);\n      }\n    }\n    return () => {\n      if (recordingTimerRef.current) {\n        clearInterval(recordingTimerRef.current);\n      }\n    };\n  }, [isRecording]);\n\n  // Video playback effect\n  useEffect(() => {\n    let playbackInterval;\n    if (isPlaying && frameBuffer.length > 0) {\n      playbackInterval = setInterval(() => {\n        setCurrentFrameIndex(prev => {\n          if (prev < frameBuffer.length - 1) {\n            return prev + 1;\n          } else {\n            setIsPlaying(false);\n            return prev;\n          }\n        });\n      }, 1000 / PLAYBACK_FPS);\n    }\n    return () => {\n      if (playbackInterval) clearInterval(playbackInterval);\n    };\n  }, [isPlaying, frameBuffer]);\n\n  // Update processed video when frame changes\n  useEffect(() => {\n    if (frameBuffer.length > 0 && currentFrameIndex < frameBuffer.length) {\n      setProcessedVideo(frameBuffer[currentFrameIndex]);\n    }\n  }, [currentFrameIndex, frameBuffer]);\n\n  // Handle video file selection\n  const handleVideoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setVideoFile(file);\n      setVideoPreview(URL.createObjectURL(file));\n      setProcessedVideo(null);\n      setVideoResults(null);\n      setAllDetections([]);\n      setError('');\n    }\n  };\n\n  // Handle camera activation\n  const toggleCamera = () => {\n    setCameraActive(!cameraActive);\n    if (!cameraActive) {\n      setVideoFile(null);\n      setVideoPreview(null);\n      setProcessedVideo(null);\n      setVideoResults(null);\n      setAllDetections([]);\n      setError('');\n    }\n  };\n\n  // Start recording\n  const handleStartRecording = async () => {\n    if (!webcamRef.current || !webcamRef.current.stream) {\n      setError('Camera not available');\n      return;\n    }\n    try {\n      setRecordedChunks([]);\n      setRecordingTime(0);\n      setIsRecording(true);\n      setError('');\n      const mediaRecorder = new MediaRecorder(webcamRef.current.stream, {\n        mimeType: 'video/webm'\n      });\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.ondataavailable = event => {\n        if (event.data.size > 0) {\n          setRecordedChunks(prev => [...prev, event.data]);\n        }\n      };\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(recordedChunks, {\n          type: 'video/webm'\n        });\n        const file = new File([blob], `recorded_video_${Date.now()}.webm`, {\n          type: 'video/webm'\n        });\n        setVideoFile(file);\n        setVideoPreview(URL.createObjectURL(blob));\n        setIsRecording(false);\n        setRecordingTime(0);\n      };\n      mediaRecorder.start();\n    } catch (error) {\n      setError('Failed to start recording: ' + error.message);\n      setIsRecording(false);\n    }\n  };\n\n  // Stop recording\n  const handleStopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setRecordingTime(0);\n    }\n  };\n\n  // Toggle camera orientation\n  const toggleCameraOrientation = () => {\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\n  };\n\n  // Check if ready for processing\n  const isReadyForProcessing = () => {\n    return inputSource === 'video' && videoFile || inputSource === 'camera' && videoFile;\n  };\n\n  // Handle video processing\n  const handleProcess = async () => {\n    if (!isReadyForProcessing()) {\n      setError('Please provide a video file first');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    setIsProcessing(true);\n    setShouldStop(false);\n    setIsBuffering(true);\n    setIsPlaying(false);\n    setFrameBuffer([]);\n    setCurrentFrameIndex(0);\n    setProcessingProgress(0);\n    setAllDetections([]);\n    setProcessedVideo(null); // Reset processed video\n    setVideoResults(null); // Reset video results\n\n    try {\n      const formData = new FormData();\n      formData.append('video', videoFile);\n      formData.append('selectedModel', selectedModel);\n      formData.append('coordinates', coordinates);\n      console.log('Starting video processing with model:', selectedModel);\n\n      // Create FormData for SSE request\n      const sseUrl = '/api/pavement/detect-video';\n\n      // Use fetch for SSE with FormData\n      const response = await fetch(sseUrl, {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      // Handle SSE stream\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder();\n      const processStream = async () => {\n        try {\n          while (true) {\n            const {\n              done,\n              value\n            } = await reader.read();\n            if (done) {\n              console.log('Stream ended naturally');\n              setIsProcessing(false);\n              setLoading(false);\n              setIsBuffering(false);\n              break;\n            }\n            const chunk = decoder.decode(value, {\n              stream: true\n            });\n            const lines = chunk.split('\\n');\n            for (const line of lines) {\n              if (line.startsWith('data: ')) {\n                try {\n                  const data = JSON.parse(line.substring(6));\n                  console.log('Received SSE data:', {\n                    hasFrame: !!data.frame,\n                    frameLength: data.frame ? data.frame.length : 0,\n                    progress: data.progress,\n                    frameCount: data.frame_count,\n                    detections: data.detections ? data.detections.length : 0\n                  });\n                  if (data.success === false) {\n                    setError(data.message || 'Video processing failed');\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    return;\n                  }\n                  if (data.frame && typeof data.frame === 'string' && data.frame.length > 1000) {\n                    // Update frame buffer and current index for real-time display\n                    setFrameBuffer(prev => {\n                      const newBuffer = [...prev, data.frame];\n\n                      // Update current frame index to the latest frame for live preview\n                      setCurrentFrameIndex(newBuffer.length - 1);\n\n                      // Start showing frames immediately\n                      if (newBuffer.length === 1) {\n                        setIsBuffering(false);\n                        setIsPlaying(false); // Don't auto-play during live processing\n                        setProcessedVideo(data.frame); // Show the first frame immediately\n                      }\n                      return newBuffer;\n                    });\n\n                    // Update the displayed frame for real-time preview\n                    setProcessedVideo(data.frame);\n                  }\n\n                  // Update progress\n                  if (data.progress !== undefined) {\n                    setProcessingProgress(data.progress);\n                    console.log(`Processing progress: ${data.progress.toFixed(1)}%`);\n                  }\n\n                  // Update detections\n                  if (data.detections && data.detections.length > 0) {\n                    setAllDetections(prev => [...prev, ...data.detections]);\n                  }\n\n                  // Handle final results\n                  if (data.all_detections) {\n                    setVideoResults(data);\n                    setAllDetections(data.all_detections);\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    setProcessingProgress(100); // Ensure progress shows 100%\n\n                    // Reset to first frame for playback after processing\n                    setCurrentFrameIndex(0);\n                    setIsPlaying(false);\n                    console.log('Video processing completed');\n                    console.log(`Total unique detections: ${data.total_unique_detections || data.all_detections.length}`);\n                    console.log(`Total frame detections: ${data.total_frame_detections || data.all_detections.length}`);\n                    console.log(`Total frames processed: ${frameBuffer.length}`);\n                    return;\n                  }\n\n                  // Handle explicit end signal\n                  if (data.end) {\n                    console.log('Received end signal, closing stream');\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    return;\n                  }\n                } catch (parseError) {\n                  console.warn('Error parsing SSE data:', parseError);\n                }\n              }\n            }\n          }\n        } catch (streamError) {\n          console.error('Stream processing error:', streamError);\n          setError('Error processing video stream');\n          setIsProcessing(false);\n          setLoading(false);\n          setIsBuffering(false);\n        } finally {\n          // Clean up reader\n          if (reader) {\n            try {\n              reader.releaseLock();\n            } catch (e) {\n              console.warn('Error releasing reader lock:', e);\n            }\n          }\n        }\n      };\n      processStream();\n    } catch (error) {\n      console.error('Video processing error:', error);\n      setError(error.message || 'Video processing failed');\n      setLoading(false);\n      setIsProcessing(false);\n    }\n  };\n\n  // Stop processing\n  const handleStopProcessing = async () => {\n    try {\n      await axios.post('/api/pavement/stop-video-processing');\n      setIsProcessing(false);\n      setShouldStop(true);\n      setIsBuffering(false);\n      setIsPlaying(false);\n      setLoading(false);\n      setError('Video processing stopped');\n    } catch (error) {\n      console.error('Error stopping processing:', error);\n      setError('Failed to stop processing');\n    }\n  };\n\n  // Reset all\n  const handleReset = () => {\n    setVideoFile(null);\n    setVideoPreview(null);\n    setProcessedVideo(null);\n    setVideoResults(null);\n    setAllDetections([]);\n    setFrameBuffer([]);\n    setCurrentFrameIndex(0);\n    setIsProcessing(false);\n    setShouldStop(false);\n    setIsBuffering(false);\n    setIsPlaying(false);\n    setProcessingProgress(0);\n    setError('');\n    setSelectedModel('All');\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Playback controls\n  const handlePlayPause = () => setIsPlaying(!isPlaying);\n  const handleRewind = () => setCurrentFrameIndex(Math.max(currentFrameIndex - 5, 0));\n  const handleForward = () => setCurrentFrameIndex(Math.min(currentFrameIndex + 5, frameBuffer.length - 1));\n\n  // Group detections by type\n  const getDetectionSummary = () => {\n    const summary = {};\n    allDetections.forEach(det => {\n      summary[det.type] = (summary[det.type] || 0) + 1;\n    });\n    return summary;\n  };\n\n  // Get tracking statistics\n  const getTrackingStats = () => {\n    if (videoResults) {\n      return {\n        uniqueDetections: videoResults.total_unique_detections || allDetections.length,\n        frameDetections: videoResults.total_frame_detections || allDetections.length,\n        duplicatesRemoved: (videoResults.total_frame_detections || allDetections.length) - (videoResults.total_unique_detections || allDetections.length)\n      };\n    }\n    return {\n      uniqueDetections: allDetections.length,\n      frameDetections: allDetections.length,\n      duplicatesRemoved: 0\n    };\n  };\n\n  // Format time\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-defect-detection\",\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-primary text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Video Defect Detection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              className: \"mb-3\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Detection Model\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: selectedModel,\n                onChange: e => setSelectedModel(e.target.value),\n                disabled: isProcessing,\n                children: modelOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Input Source\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: inputSource,\n                onChange: e => setInputSource(e.target.value),\n                disabled: isProcessing,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"video\",\n                  children: \"Video Upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"camera\",\n                  children: \"Live Camera Recording\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this), inputSource === 'video' && /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Upload Video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"file\",\n                accept: \"video/*\",\n                onChange: handleVideoChange,\n                ref: fileInputRef,\n                disabled: isProcessing\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this), videoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"video\", {\n                  src: videoPreview,\n                  controls: true,\n                  className: \"video-preview\",\n                  style: {\n                    maxHeight: '200px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this), inputSource === 'camera' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: cameraActive ? \"danger\" : \"info\",\n                  onClick: toggleCamera,\n                  disabled: isProcessing,\n                  children: cameraActive ? 'Stop Camera' : 'Start Camera'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this), isMobile && cameraActive && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-secondary\",\n                  onClick: toggleCameraOrientation,\n                  size: \"sm\",\n                  children: \"Rotate Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this), cameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"webcam-container\",\n                children: [/*#__PURE__*/_jsxDEV(Webcam, {\n                  audio: false,\n                  ref: webcamRef,\n                  screenshotFormat: \"image/jpeg\",\n                  width: \"100%\",\n                  height: \"auto\",\n                  videoConstraints: {\n                    width: 640,\n                    height: 480,\n                    facingMode: cameraOrientation\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2\",\n                  children: !isRecording ? /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"success\",\n                    onClick: handleStartRecording,\n                    disabled: isProcessing,\n                    children: \"Start Recording\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"danger\",\n                      onClick: handleStopRecording,\n                      children: \"Stop Recording\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-danger\",\n                      children: [\"Recording: \", formatTime(recordingTime), \" / \", formatTime(MAX_RECORDING_TIME)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 21\n              }, this), videoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: /*#__PURE__*/_jsxDEV(\"video\", {\n                  src: videoPreview,\n                  controls: true,\n                  className: \"video-preview\",\n                  style: {\n                    maxHeight: '200px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"action-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: handleProcess,\n                disabled: !isReadyForProcessing() || isProcessing,\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    size: \"sm\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 23\n                  }, this), \"Processing...\"]\n                }, void 0, true) : 'Process Video'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this), isProcessing && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"warning\",\n                onClick: handleStopProcessing,\n                children: \"Stop Processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                onClick: handleReset,\n                disabled: isProcessing,\n                children: \"Reset\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this), isProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Processing Progress:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [processingProgress.toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress mt-1\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress-bar\",\n                  role: \"progressbar\",\n                  style: {\n                    width: `${processingProgress}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: [processedVideo && /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-success text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Processed Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"processed-video-container\",\n              children: [isBuffering && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"processing-overlay\",\n                children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                  animation: \"border\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ms-2\",\n                  children: \"Buffering video...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                src: processedVideo.startsWith('data:') ? processedVideo : `data:image/jpeg;base64,${processedVideo}`,\n                alt: \"Processed frame\",\n                style: {\n                  maxWidth: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this), frameBuffer.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-controls mt-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex gap-2 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"outline-primary\",\n                    onClick: handleRewind,\n                    children: \"\\u23EA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 683,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"outline-primary\",\n                    onClick: handlePlayPause,\n                    children: isPlaying ? '⏸️' : '▶️'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"outline-primary\",\n                    onClick: handleForward,\n                    children: \"\\u23E9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Form.Range, {\n                  min: 0,\n                  max: frameBuffer.length - 1,\n                  value: currentFrameIndex,\n                  onChange: e => setCurrentFrameIndex(Number(e.target.value))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center small text-muted\",\n                  children: [\"Frame \", currentFrameIndex + 1, \" of \", frameBuffer.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 13\n        }, this), allDetections.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Detection Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detection-summary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Detection Summary:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: Object.entries(getDetectionSummary()).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-secondary me-1\",\n                  children: [type, \": \", count]\n                }, type, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tracking-stats\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Tracking Stats:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 23\n                  }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-success me-1\",\n                    children: [\"Unique: \", getTrackingStats().uniqueDetections]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 733,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-info me-1\",\n                    children: [\"Total Frames: \", getTrackingStats().frameDetections]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-warning\",\n                    children: [\"Duplicates Removed: \", getTrackingStats().duplicatesRemoved]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 17\n            }, this), (() => {\n              const potholeDetections = allDetections.filter(d => d.type === 'Pothole');\n              const crackDetections = allDetections.filter(d => d.type.includes('Crack'));\n              const kerbDetections = allDetections.filter(d => d.type.includes('Kerb'));\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [(selectedModel === 'All' || selectedModel === 'Potholes') && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section potholes mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-danger\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83D\\uDD73\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 758,\n                      columnNumber: 29\n                    }, this), \"Potholes Detected: \", potholeDetections.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 757,\n                    columnNumber: 27\n                  }, this), potholeDetections.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detection-table-container\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      striped: true,\n                      bordered: true,\n                      hover: true,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 766,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Frame\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 767,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Timestamp (s)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 768,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Confidence\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 769,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area (cm\\xB2)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 770,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Width (cm)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 771,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Length (cm)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 772,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Max Depth (cm)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 773,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Volume (cm\\xB3)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 774,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Volume Range\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 775,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 765,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 764,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: potholeDetections.map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.track_id || index + 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 781,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.frame\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 782,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.timestamp ? detection.timestamp.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 783,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.confidence ? detection.confidence.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 784,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 785,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.width_cm ? detection.width_cm.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 786,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.length_cm ? detection.length_cm.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 787,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.max_depth_cm ? detection.max_depth_cm.toFixed(2) : detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 788,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.volume ? detection.volume.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 789,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.volume_range || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 790,\n                            columnNumber: 39\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 780,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 778,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 763,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No potholes detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 25\n                }, this), (selectedModel === 'All' || selectedModel === 'Alligator Cracks') && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section cracks mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-success\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83E\\uDEA8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 29\n                    }, this), \"Cracks Detected: \", crackDetections.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 27\n                  }, this), crackDetections.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detection-table-container\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      striped: true,\n                      bordered: true,\n                      hover: true,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 814,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Type\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 815,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area (cm\\xB2)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 816,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area Range\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 817,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 813,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 812,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: crackDetections.map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.track_id || index + 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 823,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.type\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 824,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 825,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.area_range || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 826,\n                            columnNumber: 39\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 822,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 820,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 811,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 810,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No cracks detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 25\n                }, this), (selectedModel === 'All' || selectedModel === 'Kerbs') && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section kerbs mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-primary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83D\\uDEA7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 29\n                    }, this), \"Kerbs Detected: \", kerbDetections.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 27\n                  }, this), kerbDetections.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detection-table-container\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      striped: true,\n                      bordered: true,\n                      hover: true,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 850,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Type\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 851,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Condition\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 852,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Length\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 853,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 849,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: kerbDetections.map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.track_id || index + 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 859,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.kerb_type || 'Concrete Kerb'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 860,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.condition || detection.type\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 861,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.length_m ? detection.length_m.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 862,\n                            columnNumber: 39\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 858,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 856,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 847,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No kerbs detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 21\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 459,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoDefectDetection, \"4BTUBSF7Dp0PLMjIRx/ul5V3xgg=\", false, function () {\n  return [useResponsive];\n});\n_c = VideoDefectDetection;\nexport default VideoDefectDetection;\nvar _c;\n$RefreshReg$(_c, \"VideoDefectDetection\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "Spinner", "Table", "Row", "Col", "axios", "Webcam", "useResponsive", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoDefectDetection", "_s", "selected<PERSON><PERSON>l", "setSelectedModel", "videoFile", "setVideoFile", "videoPreview", "setVideoPreview", "processedVideo", "setProcessedVideo", "loading", "setLoading", "error", "setError", "isProcessing", "setIsProcessing", "shouldStop", "setShouldStop", "coordinates", "setCoordinates", "inputSource", "setInputSource", "cameraActive", "setCameraActive", "cameraOrientation", "setCameraOrientation", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "recordedChunks", "setRecordedChunks", "frameBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentFrameIndex", "setCurrentFrameIndex", "isBuffering", "setIsBuffering", "isPlaying", "setIsPlaying", "processingProgress", "setProcessingProgress", "allDetections", "setAllDetections", "videoResults", "setVideoResults", "webcamRef", "fileInputRef", "recordingTimerRef", "mediaRecorderRef", "isMobile", "BUFFER_SIZE", "PLAYBACK_FPS", "MAX_RECORDING_TIME", "modelOptions", "value", "label", "navigator", "geolocation", "getCurrentPosition", "position", "latitude", "longitude", "coords", "toFixed", "err", "console", "current", "setInterval", "prev", "handleStopRecording", "clearInterval", "playbackInterval", "length", "handleVideoChange", "e", "file", "target", "files", "URL", "createObjectURL", "toggleCamera", "handleStartRecording", "stream", "mediaRecorder", "MediaRecorder", "mimeType", "ondataavailable", "event", "data", "size", "onstop", "blob", "Blob", "type", "File", "Date", "now", "start", "message", "stop", "toggleCameraOrientation", "isReadyForProcessing", "handleProcess", "formData", "FormData", "append", "log", "sseUrl", "response", "fetch", "method", "body", "ok", "Error", "status", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "processStream", "done", "read", "chunk", "decode", "lines", "split", "line", "startsWith", "JSON", "parse", "substring", "<PERSON><PERSON><PERSON><PERSON>", "frame", "frameLength", "progress", "frameCount", "frame_count", "detections", "success", "new<PERSON>uffer", "undefined", "all_detections", "total_unique_detections", "total_frame_detections", "end", "parseError", "warn", "streamError", "releaseLock", "handleStopProcessing", "post", "handleReset", "handlePlayPause", "handleRewind", "Math", "max", "handleForward", "min", "getDetectionSummary", "summary", "for<PERSON>ach", "det", "getTrackingStats", "uniqueDetections", "frameDetections", "duplicates<PERSON><PERSON>oved", "formatTime", "seconds", "mins", "floor", "secs", "toString", "padStart", "className", "children", "md", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "variant", "Group", "Label", "Select", "onChange", "disabled", "map", "option", "Control", "accept", "ref", "src", "controls", "style", "maxHeight", "onClick", "audio", "screenshotFormat", "width", "height", "videoConstraints", "facingMode", "role", "animation", "alt", "max<PERSON><PERSON><PERSON>", "Range", "Number", "Object", "entries", "count", "potholeDetections", "filter", "d", "crackDetections", "includes", "kerbDetections", "striped", "bordered", "hover", "detection", "index", "track_id", "timestamp", "confidence", "area_cm2", "width_cm", "length_cm", "max_depth_cm", "depth_cm", "volume", "volume_range", "area_range", "kerb_type", "condition", "length_m", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTA_GIT/LTA/frontend/src/components/VideoDefectDetection.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Card, Button, Form, Alert, Spinner, Table, Row, Col } from 'react-bootstrap';\nimport axios from 'axios';\nimport Webcam from 'react-webcam';\nimport useResponsive from '../hooks/useResponsive';\nimport './VideoDefectDetection.css';\n\nconst VideoDefectDetection = () => {\n  const [selectedModel, setSelectedModel] = useState('All');\n  const [videoFile, setVideoFile] = useState(null);\n  const [videoPreview, setVideoPreview] = useState(null);\n  const [processedVideo, setProcessedVideo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [shouldStop, setShouldStop] = useState(false);\n  const [coordinates, setCoordinates] = useState('Not Available');\n  const [inputSource, setInputSource] = useState('video');\n  const [cameraActive, setCameraActive] = useState(false);\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [recordedChunks, setRecordedChunks] = useState([]);\n  \n  // Video processing states\n  const [frameBuffer, setFrameBuffer] = useState([]);\n  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);\n  const [isBuffering, setIsBuffering] = useState(false);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [processingProgress, setProcessingProgress] = useState(0);\n  const [allDetections, setAllDetections] = useState([]);\n  const [videoResults, setVideoResults] = useState(null);\n  \n  const webcamRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const recordingTimerRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const { isMobile } = useResponsive();\n  \n  const BUFFER_SIZE = 10;\n  const PLAYBACK_FPS = 15;\n  const MAX_RECORDING_TIME = 60; // 1 minute limit\n\n  // Available models\n  const modelOptions = [\n    { value: 'All', label: 'All (detect all types of defects)' },\n    { value: 'Potholes', label: 'Potholes' },\n    { value: 'Alligator Cracks', label: 'Alligator Cracks' },\n    { value: 'Kerbs', label: 'Kerbs' }\n  ];\n\n  // Get user location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const { latitude, longitude } = position.coords;\n          setCoordinates(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);\n        },\n        (err) => {\n          console.error(\"Error getting location:\", err);\n          setCoordinates('Location unavailable');\n        }\n      );\n    }\n  }, []);\n\n  // Recording timer\n  useEffect(() => {\n    if (isRecording) {\n      recordingTimerRef.current = setInterval(() => {\n        setRecordingTime(prev => {\n          if (prev >= MAX_RECORDING_TIME) {\n            handleStopRecording();\n            return MAX_RECORDING_TIME;\n          }\n          return prev + 1;\n        });\n      }, 1000);\n    } else {\n      if (recordingTimerRef.current) {\n        clearInterval(recordingTimerRef.current);\n      }\n    }\n    \n    return () => {\n      if (recordingTimerRef.current) {\n        clearInterval(recordingTimerRef.current);\n      }\n    };\n  }, [isRecording]);\n\n  // Video playback effect\n  useEffect(() => {\n    let playbackInterval;\n    if (isPlaying && frameBuffer.length > 0) {\n      playbackInterval = setInterval(() => {\n        setCurrentFrameIndex(prev => {\n          if (prev < frameBuffer.length - 1) {\n            return prev + 1;\n          } else {\n            setIsPlaying(false);\n            return prev;\n          }\n        });\n      }, 1000 / PLAYBACK_FPS);\n    }\n    return () => {\n      if (playbackInterval) clearInterval(playbackInterval);\n    };\n  }, [isPlaying, frameBuffer]);\n\n  // Update processed video when frame changes\n  useEffect(() => {\n    if (frameBuffer.length > 0 && currentFrameIndex < frameBuffer.length) {\n      setProcessedVideo(frameBuffer[currentFrameIndex]);\n    }\n  }, [currentFrameIndex, frameBuffer]);\n\n  // Handle video file selection\n  const handleVideoChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setVideoFile(file);\n      setVideoPreview(URL.createObjectURL(file));\n      setProcessedVideo(null);\n      setVideoResults(null);\n      setAllDetections([]);\n      setError('');\n    }\n  };\n\n  // Handle camera activation\n  const toggleCamera = () => {\n    setCameraActive(!cameraActive);\n    if (!cameraActive) {\n      setVideoFile(null);\n      setVideoPreview(null);\n      setProcessedVideo(null);\n      setVideoResults(null);\n      setAllDetections([]);\n      setError('');\n    }\n  };\n\n  // Start recording\n  const handleStartRecording = async () => {\n    if (!webcamRef.current || !webcamRef.current.stream) {\n      setError('Camera not available');\n      return;\n    }\n\n    try {\n      setRecordedChunks([]);\n      setRecordingTime(0);\n      setIsRecording(true);\n      setError('');\n\n      const mediaRecorder = new MediaRecorder(webcamRef.current.stream, {\n        mimeType: 'video/webm'\n      });\n\n      mediaRecorderRef.current = mediaRecorder;\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          setRecordedChunks(prev => [...prev, event.data]);\n        }\n      };\n\n      mediaRecorder.onstop = () => {\n        const blob = new Blob(recordedChunks, { type: 'video/webm' });\n        const file = new File([blob], `recorded_video_${Date.now()}.webm`, { type: 'video/webm' });\n        setVideoFile(file);\n        setVideoPreview(URL.createObjectURL(blob));\n        setIsRecording(false);\n        setRecordingTime(0);\n      };\n\n      mediaRecorder.start();\n    } catch (error) {\n      setError('Failed to start recording: ' + error.message);\n      setIsRecording(false);\n    }\n  };\n\n  // Stop recording\n  const handleStopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setRecordingTime(0);\n    }\n  };\n\n  // Toggle camera orientation\n  const toggleCameraOrientation = () => {\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\n  };\n\n  // Check if ready for processing\n  const isReadyForProcessing = () => {\n    return (inputSource === 'video' && videoFile) || \n           (inputSource === 'camera' && videoFile);\n  };\n\n  // Handle video processing\n  const handleProcess = async () => {\n    if (!isReadyForProcessing()) {\n      setError('Please provide a video file first');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setIsProcessing(true);\n    setShouldStop(false);\n    setIsBuffering(true);\n    setIsPlaying(false);\n    setFrameBuffer([]);\n    setCurrentFrameIndex(0);\n    setProcessingProgress(0);\n    setAllDetections([]);\n    setProcessedVideo(null); // Reset processed video\n    setVideoResults(null);   // Reset video results\n\n    try {\n      const formData = new FormData();\n      formData.append('video', videoFile);\n      formData.append('selectedModel', selectedModel);\n      formData.append('coordinates', coordinates);\n\n      console.log('Starting video processing with model:', selectedModel);\n\n      // Create FormData for SSE request\n      const sseUrl = '/api/pavement/detect-video';\n      \n      // Use fetch for SSE with FormData\n      const response = await fetch(sseUrl, {\n        method: 'POST',\n        body: formData\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      // Handle SSE stream\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder();\n\n      const processStream = async () => {\n        try {\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) {\n              console.log('Stream ended naturally');\n              setIsProcessing(false);\n              setLoading(false);\n              setIsBuffering(false);\n              break;\n            }\n\n            const chunk = decoder.decode(value, { stream: true });\n            const lines = chunk.split('\\n');\n\n            for (const line of lines) {\n              if (line.startsWith('data: ')) {\n                try {\n                  const data = JSON.parse(line.substring(6));\n                  console.log('Received SSE data:', {\n                    hasFrame: !!data.frame,\n                    frameLength: data.frame ? data.frame.length : 0,\n                    progress: data.progress,\n                    frameCount: data.frame_count,\n                    detections: data.detections ? data.detections.length : 0\n                  });\n\n                  if (data.success === false) {\n                    setError(data.message || 'Video processing failed');\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    return;\n                  }\n\n                  if (data.frame && typeof data.frame === 'string' && data.frame.length > 1000) {\n                    // Update frame buffer and current index for real-time display\n                    setFrameBuffer(prev => {\n                      const newBuffer = [...prev, data.frame];\n                      \n                      // Update current frame index to the latest frame for live preview\n                      setCurrentFrameIndex(newBuffer.length - 1);\n                      \n                      // Start showing frames immediately\n                      if (newBuffer.length === 1) {\n                        setIsBuffering(false);\n                        setIsPlaying(false); // Don't auto-play during live processing\n                        setProcessedVideo(data.frame); // Show the first frame immediately\n                      }\n                      \n                      return newBuffer;\n                    });\n                    \n                    // Update the displayed frame for real-time preview\n                    setProcessedVideo(data.frame);\n                  }\n\n                  // Update progress\n                  if (data.progress !== undefined) {\n                    setProcessingProgress(data.progress);\n                    console.log(`Processing progress: ${data.progress.toFixed(1)}%`);\n                  }\n\n                  // Update detections\n                  if (data.detections && data.detections.length > 0) {\n                    setAllDetections(prev => [...prev, ...data.detections]);\n                  }\n\n                  // Handle final results\n                  if (data.all_detections) {\n                    setVideoResults(data);\n                    setAllDetections(data.all_detections);\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    setProcessingProgress(100); // Ensure progress shows 100%\n                    \n                    // Reset to first frame for playback after processing\n                    setCurrentFrameIndex(0);\n                    setIsPlaying(false);\n                    \n                    console.log('Video processing completed');\n                    console.log(`Total unique detections: ${data.total_unique_detections || data.all_detections.length}`);\n                    console.log(`Total frame detections: ${data.total_frame_detections || data.all_detections.length}`);\n                    console.log(`Total frames processed: ${frameBuffer.length}`);\n                    return;\n                  }\n\n                  // Handle explicit end signal\n                  if (data.end) {\n                    console.log('Received end signal, closing stream');\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    return;\n                  }\n                } catch (parseError) {\n                  console.warn('Error parsing SSE data:', parseError);\n                }\n              }\n            }\n          }\n        } catch (streamError) {\n          console.error('Stream processing error:', streamError);\n          setError('Error processing video stream');\n          setIsProcessing(false);\n          setLoading(false);\n          setIsBuffering(false);\n        } finally {\n          // Clean up reader\n          if (reader) {\n            try {\n              reader.releaseLock();\n            } catch (e) {\n              console.warn('Error releasing reader lock:', e);\n            }\n          }\n        }\n      };\n\n      processStream();\n\n    } catch (error) {\n      console.error('Video processing error:', error);\n      setError(error.message || 'Video processing failed');\n      setLoading(false);\n      setIsProcessing(false);\n    }\n  };\n\n  // Stop processing\n  const handleStopProcessing = async () => {\n    try {\n      await axios.post('/api/pavement/stop-video-processing');\n      \n      setIsProcessing(false);\n      setShouldStop(true);\n      setIsBuffering(false);\n      setIsPlaying(false);\n      setLoading(false);\n      setError('Video processing stopped');\n    } catch (error) {\n      console.error('Error stopping processing:', error);\n      setError('Failed to stop processing');\n    }\n  };\n\n  // Reset all\n  const handleReset = () => {\n    setVideoFile(null);\n    setVideoPreview(null);\n    setProcessedVideo(null);\n    setVideoResults(null);\n    setAllDetections([]);\n    setFrameBuffer([]);\n    setCurrentFrameIndex(0);\n    setIsProcessing(false);\n    setShouldStop(false);\n    setIsBuffering(false);\n    setIsPlaying(false);\n    setProcessingProgress(0);\n    setError('');\n    setSelectedModel('All');\n    \n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Playback controls\n  const handlePlayPause = () => setIsPlaying(!isPlaying);\n  const handleRewind = () => setCurrentFrameIndex(Math.max(currentFrameIndex - 5, 0));\n  const handleForward = () => setCurrentFrameIndex(Math.min(currentFrameIndex + 5, frameBuffer.length - 1));\n\n  // Group detections by type\n  const getDetectionSummary = () => {\n    const summary = {};\n    allDetections.forEach(det => {\n      summary[det.type] = (summary[det.type] || 0) + 1;\n    });\n    return summary;\n  };\n\n  // Get tracking statistics\n  const getTrackingStats = () => {\n    if (videoResults) {\n      return {\n        uniqueDetections: videoResults.total_unique_detections || allDetections.length,\n        frameDetections: videoResults.total_frame_detections || allDetections.length,\n        duplicatesRemoved: (videoResults.total_frame_detections || allDetections.length) - (videoResults.total_unique_detections || allDetections.length)\n      };\n    }\n    return {\n      uniqueDetections: allDetections.length,\n      frameDetections: allDetections.length,\n      duplicatesRemoved: 0\n    };\n  };\n\n  // Format time\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  return (\n    <div className=\"video-defect-detection\">\n      <Row>\n        <Col md={6}>\n          <Card className=\"mb-4\">\n            <Card.Header className=\"bg-primary text-white\">\n              <h5 className=\"mb-0\">Video Defect Detection</h5>\n            </Card.Header>\n            <Card.Body>\n              {error && (\n                <Alert variant=\"danger\" className=\"mb-3\">\n                  {error}\n                </Alert>\n              )}\n\n              {/* Model Selection */}\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Detection Model</Form.Label>\n                <Form.Select\n                  value={selectedModel}\n                  onChange={(e) => setSelectedModel(e.target.value)}\n                  disabled={isProcessing}\n                >\n                  {modelOptions.map(option => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </Form.Select>\n              </Form.Group>\n\n              {/* Input Source Selection */}\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Input Source</Form.Label>\n                <Form.Select\n                  value={inputSource}\n                  onChange={(e) => setInputSource(e.target.value)}\n                  disabled={isProcessing}\n                >\n                  <option value=\"video\">Video Upload</option>\n                  <option value=\"camera\">Live Camera Recording</option>\n                </Form.Select>\n              </Form.Group>\n\n              {/* Video Upload */}\n              {inputSource === 'video' && (\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Upload Video</Form.Label>\n                  <Form.Control\n                    type=\"file\"\n                    accept=\"video/*\"\n                    onChange={handleVideoChange}\n                    ref={fileInputRef}\n                    disabled={isProcessing}\n                  />\n                  {videoPreview && (\n                    <div className=\"mt-3\">\n                      <video\n                        src={videoPreview}\n                        controls\n                        className=\"video-preview\"\n                        style={{ maxHeight: '200px' }}\n                      />\n                    </div>\n                  )}\n                </Form.Group>\n              )}\n\n              {/* Camera Recording */}\n              {inputSource === 'camera' && (\n                <div className=\"mb-3\">\n                  <div className=\"d-flex gap-2 mb-2\">\n                    <Button\n                      variant={cameraActive ? \"danger\" : \"info\"}\n                      onClick={toggleCamera}\n                      disabled={isProcessing}\n                    >\n                      {cameraActive ? 'Stop Camera' : 'Start Camera'}\n                    </Button>\n                    {isMobile && cameraActive && (\n                      <Button\n                        variant=\"outline-secondary\"\n                        onClick={toggleCameraOrientation}\n                        size=\"sm\"\n                      >\n                        Rotate Camera\n                      </Button>\n                    )}\n                  </div>\n\n                  {cameraActive && (\n                    <div className=\"webcam-container\">\n                      <Webcam\n                        audio={false}\n                        ref={webcamRef}\n                        screenshotFormat=\"image/jpeg\"\n                        width=\"100%\"\n                        height=\"auto\"\n                        videoConstraints={{\n                          width: 640,\n                          height: 480,\n                          facingMode: cameraOrientation\n                        }}\n                      />\n                      \n                      <div className=\"mt-2\">\n                        {!isRecording ? (\n                          <Button\n                            variant=\"success\"\n                            onClick={handleStartRecording}\n                            disabled={isProcessing}\n                          >\n                            Start Recording\n                          </Button>\n                        ) : (\n                          <div className=\"d-flex align-items-center gap-2\">\n                            <Button\n                              variant=\"danger\"\n                              onClick={handleStopRecording}\n                            >\n                              Stop Recording\n                            </Button>\n                            <span className=\"text-danger\">\n                              Recording: {formatTime(recordingTime)} / {formatTime(MAX_RECORDING_TIME)}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  {videoPreview && (\n                    <div className=\"mt-3\">\n                      <video\n                        src={videoPreview}\n                        controls\n                        className=\"video-preview\"\n                        style={{ maxHeight: '200px' }}\n                      />\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* Action Buttons */}\n              <div className=\"action-buttons\">\n                <Button\n                  variant=\"primary\"\n                  onClick={handleProcess}\n                  disabled={!isReadyForProcessing() || isProcessing}\n                >\n                  {loading ? (\n                    <>\n                      <Spinner size=\"sm\" className=\"me-2\" />\n                      Processing...\n                    </>\n                  ) : (\n                    'Process Video'\n                  )}\n                </Button>\n                \n                {isProcessing && (\n                  <Button\n                    variant=\"warning\"\n                    onClick={handleStopProcessing}\n                  >\n                    Stop Processing\n                  </Button>\n                )}\n                \n                <Button\n                  variant=\"secondary\"\n                  onClick={handleReset}\n                  disabled={isProcessing}\n                >\n                  Reset\n                </Button>\n              </div>\n\n              {/* Processing Progress */}\n              {isProcessing && (\n                <div className=\"mt-3\">\n                  <div className=\"d-flex justify-content-between\">\n                    <span>Processing Progress:</span>\n                    <span>{processingProgress.toFixed(1)}%</span>\n                  </div>\n                  <div className=\"progress mt-1\">\n                    <div\n                      className=\"progress-bar\"\n                      role=\"progressbar\"\n                      style={{ width: `${processingProgress}%` }}\n                    ></div>\n                  </div>\n                </div>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n\n        <Col md={6}>\n          {/* Processed Video Display */}\n          {processedVideo && (\n            <Card className=\"mb-4\">\n              <Card.Header className=\"bg-success text-white\">\n                <h5 className=\"mb-0\">Processed Video</h5>\n              </Card.Header>\n              <Card.Body>\n                <div className=\"processed-video-container\">\n                  {isBuffering && (\n                    <div className=\"processing-overlay\">\n                      <Spinner animation=\"border\" />\n                      <span className=\"ms-2\">Buffering video...</span>\n                    </div>\n                  )}\n                  \n                  <img\n                    src={processedVideo.startsWith('data:') ? processedVideo : `data:image/jpeg;base64,${processedVideo}`}\n                    alt=\"Processed frame\"\n                    style={{ maxWidth: '100%' }}\n                  />\n                  \n                  {/* Video Controls */}\n                  {frameBuffer.length > 0 && (\n                    <div className=\"video-controls mt-3\">\n                      <div className=\"d-flex gap-2 mb-2\">\n                        <Button size=\"sm\" variant=\"outline-primary\" onClick={handleRewind}>\n                          ⏪\n                        </Button>\n                        <Button size=\"sm\" variant=\"outline-primary\" onClick={handlePlayPause}>\n                          {isPlaying ? '⏸️' : '▶️'}\n                        </Button>\n                        <Button size=\"sm\" variant=\"outline-primary\" onClick={handleForward}>\n                          ⏩\n                        </Button>\n                      </div>\n                      \n                      <Form.Range\n                        min={0}\n                        max={frameBuffer.length - 1}\n                        value={currentFrameIndex}\n                        onChange={(e) => setCurrentFrameIndex(Number(e.target.value))}\n                      />\n                      \n                      <div className=\"text-center small text-muted\">\n                        Frame {currentFrameIndex + 1} of {frameBuffer.length}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </Card.Body>\n            </Card>\n          )}\n\n          {/* Detection Results Table */}\n          {allDetections.length > 0 && (\n            <Card>\n              <Card.Header className=\"bg-info text-white\">\n                <h5 className=\"mb-0\">Detection Results</h5>\n              </Card.Header>\n              <Card.Body>\n                {/* Summary */}\n                <div className=\"detection-summary mb-3\">\n                  <h6>Detection Summary:</h6>\n                  <div className=\"mb-2\">\n                    {Object.entries(getDetectionSummary()).map(([type, count]) => (\n                      <span key={type} className=\"badge bg-secondary me-1\">\n                        {type}: {count}\n                      </span>\n                    ))}\n                  </div>\n                  \n                  {/* Tracking Statistics */}\n                  <div className=\"tracking-stats\">\n                    <small className=\"text-muted\">\n                      <strong>Tracking Stats:</strong> {' '}\n                      <span className=\"badge bg-success me-1\">\n                        Unique: {getTrackingStats().uniqueDetections}\n                      </span>\n                      <span className=\"badge bg-info me-1\">\n                        Total Frames: {getTrackingStats().frameDetections}\n                      </span>\n                      <span className=\"badge bg-warning\">\n                        Duplicates Removed: {getTrackingStats().duplicatesRemoved}\n                      </span>\n                    </small>\n                  </div>\n                </div>\n\n                {/* Separate Tables for Each Defect Type */}\n                {(() => {\n                  const potholeDetections = allDetections.filter(d => d.type === 'Pothole');\n                  const crackDetections = allDetections.filter(d => d.type.includes('Crack'));\n                  const kerbDetections = allDetections.filter(d => d.type.includes('Kerb'));\n\n                  return (\n                    <div>\n                      {/* Pothole Table - Show only if \"All\" or \"Potholes\" is selected */}\n                      {(selectedModel === 'All' || selectedModel === 'Potholes') && (\n                        <div className=\"defect-section potholes mb-4\">\n                          <h6 className=\"text-danger\">\n                            <span className=\"emoji\">🕳️</span>\n                            Potholes Detected: {potholeDetections.length}\n                          </h6>\n                          {potholeDetections.length > 0 ? (\n                            <div className=\"detection-table-container\">\n                              <Table striped bordered hover size=\"sm\">\n                                <thead>\n                                  <tr>\n                                    <th>ID</th>\n                                    <th>Frame</th>\n                                    <th>Timestamp (s)</th>\n                                    <th>Confidence</th>\n                                    <th>Area (cm²)</th>\n                                    <th>Width (cm)</th>\n                                    <th>Length (cm)</th>\n                                    <th>Max Depth (cm)</th>\n                                    <th>Volume (cm³)</th>\n                                    <th>Volume Range</th>\n                                  </tr>\n                                </thead>\n                                <tbody>\n                                  {potholeDetections.map((detection, index) => (\n                                    <tr key={index}>\n                                      <td>{detection.track_id || index + 1}</td>\n                                      <td>{detection.frame}</td>\n                                      <td>{detection.timestamp ? detection.timestamp.toFixed(2) : 'N/A'}</td>\n                                      <td>{detection.confidence ? detection.confidence.toFixed(2) : 'N/A'}</td>\n                                      <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\n                                      <td>{detection.width_cm ? detection.width_cm.toFixed(2) : 'N/A'}</td>\n                                      <td>{detection.length_cm ? detection.length_cm.toFixed(2) : 'N/A'}</td>\n                                      <td>{detection.max_depth_cm ? detection.max_depth_cm.toFixed(2) : (detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A')}</td>\n                                      <td>{detection.volume ? detection.volume.toFixed(2) : 'N/A'}</td>\n                                      <td>{detection.volume_range || 'N/A'}</td>\n                                    </tr>\n                                  ))}\n                                </tbody>\n                              </Table>\n                            </div>\n                          ) : (\n                            <div className=\"no-defects-message\">No potholes detected</div>\n                          )}\n                        </div>\n                      )}\n\n                      {/* Cracks Table - Show only if \"All\" or \"Alligator Cracks\" is selected */}\n                      {(selectedModel === 'All' || selectedModel === 'Alligator Cracks') && (\n                        <div className=\"defect-section cracks mb-4\">\n                          <h6 className=\"text-success\">\n                            <span className=\"emoji\">🪨</span>\n                            Cracks Detected: {crackDetections.length}\n                          </h6>\n                          {crackDetections.length > 0 ? (\n                            <div className=\"detection-table-container\">\n                              <Table striped bordered hover size=\"sm\">\n                                <thead>\n                                  <tr>\n                                    <th>ID</th>\n                                    <th>Type</th>\n                                    <th>Area (cm²)</th>\n                                    <th>Area Range</th>\n                                  </tr>\n                                </thead>\n                                <tbody>\n                                  {crackDetections.map((detection, index) => (\n                                    <tr key={index}>\n                                      <td>{detection.track_id || index + 1}</td>\n                                      <td>{detection.type}</td>\n                                      <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\n                                      <td>{detection.area_range || 'N/A'}</td>\n                                    </tr>\n                                  ))}\n                                </tbody>\n                              </Table>\n                            </div>\n                          ) : (\n                            <div className=\"no-defects-message\">No cracks detected</div>\n                          )}\n                        </div>\n                      )}\n\n                      {/* Kerbs Table - Show only if \"All\" or \"Kerbs\" is selected */}\n                      {(selectedModel === 'All' || selectedModel === 'Kerbs') && (\n                        <div className=\"defect-section kerbs mb-4\">\n                          <h6 className=\"text-primary\">\n                            <span className=\"emoji\">🚧</span>\n                            Kerbs Detected: {kerbDetections.length}\n                          </h6>\n                          {kerbDetections.length > 0 ? (\n                            <div className=\"detection-table-container\">\n                              <Table striped bordered hover size=\"sm\">\n                                <thead>\n                                  <tr>\n                                    <th>ID</th>\n                                    <th>Type</th>\n                                    <th>Condition</th>\n                                    <th>Length</th>\n                                  </tr>\n                                </thead>\n                                <tbody>\n                                  {kerbDetections.map((detection, index) => (\n                                    <tr key={index}>\n                                      <td>{detection.track_id || index + 1}</td>\n                                      <td>{detection.kerb_type || 'Concrete Kerb'}</td>\n                                      <td>{detection.condition || detection.type}</td>\n                                      <td>{detection.length_m ? detection.length_m.toFixed(2) : 'N/A'}</td>\n                                    </tr>\n                                  ))}\n                                </tbody>\n                              </Table>\n                            </div>\n                          ) : (\n                            <div className=\"no-defects-message\">No kerbs detected</div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  );\n                })()}\n              </Card.Body>\n            </Card>\n          )}\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default VideoDefectDetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACrF,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,eAAe,CAAC;EAC/D,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3C,QAAQ,CAAC,aAAa,CAAC;EACzE,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMgE,SAAS,GAAG/D,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgE,YAAY,GAAGhE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMiE,iBAAiB,GAAGjE,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMkE,gBAAgB,GAAGlE,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM;IAAEmE;EAAS,CAAC,GAAGvD,aAAa,CAAC,CAAC;EAEpC,MAAMwD,WAAW,GAAG,EAAE;EACtB,MAAMC,YAAY,GAAG,EAAE;EACvB,MAAMC,kBAAkB,GAAG,EAAE,CAAC,CAAC;;EAE/B;EACA,MAAMC,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAoC,CAAC,EAC5D;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAmB,CAAC,EACxD;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,CACnC;;EAED;EACAxE,SAAS,CAAC,MAAM;IACd,IAAIyE,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZ,MAAM;UAAEC,QAAQ;UAAEC;QAAU,CAAC,GAAGF,QAAQ,CAACG,MAAM;QAC/C5C,cAAc,CAAC,GAAG0C,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,KAAKF,SAAS,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;MACnE,CAAC,EACAC,GAAG,IAAK;QACPC,OAAO,CAACtD,KAAK,CAAC,yBAAyB,EAAEqD,GAAG,CAAC;QAC7C9C,cAAc,CAAC,sBAAsB,CAAC;MACxC,CACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnC,SAAS,CAAC,MAAM;IACd,IAAI0C,WAAW,EAAE;MACfsB,iBAAiB,CAACmB,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC5CvC,gBAAgB,CAACwC,IAAI,IAAI;UACvB,IAAIA,IAAI,IAAIhB,kBAAkB,EAAE;YAC9BiB,mBAAmB,CAAC,CAAC;YACrB,OAAOjB,kBAAkB;UAC3B;UACA,OAAOgB,IAAI,GAAG,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL,IAAIrB,iBAAiB,CAACmB,OAAO,EAAE;QAC7BI,aAAa,CAACvB,iBAAiB,CAACmB,OAAO,CAAC;MAC1C;IACF;IAEA,OAAO,MAAM;MACX,IAAInB,iBAAiB,CAACmB,OAAO,EAAE;QAC7BI,aAAa,CAACvB,iBAAiB,CAACmB,OAAO,CAAC;MAC1C;IACF,CAAC;EACH,CAAC,EAAE,CAACzC,WAAW,CAAC,CAAC;;EAEjB;EACA1C,SAAS,CAAC,MAAM;IACd,IAAIwF,gBAAgB;IACpB,IAAIlC,SAAS,IAAIN,WAAW,CAACyC,MAAM,GAAG,CAAC,EAAE;MACvCD,gBAAgB,GAAGJ,WAAW,CAAC,MAAM;QACnCjC,oBAAoB,CAACkC,IAAI,IAAI;UAC3B,IAAIA,IAAI,GAAGrC,WAAW,CAACyC,MAAM,GAAG,CAAC,EAAE;YACjC,OAAOJ,IAAI,GAAG,CAAC;UACjB,CAAC,MAAM;YACL9B,YAAY,CAAC,KAAK,CAAC;YACnB,OAAO8B,IAAI;UACb;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,GAAGjB,YAAY,CAAC;IACzB;IACA,OAAO,MAAM;MACX,IAAIoB,gBAAgB,EAAED,aAAa,CAACC,gBAAgB,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAAClC,SAAS,EAAEN,WAAW,CAAC,CAAC;;EAE5B;EACAhD,SAAS,CAAC,MAAM;IACd,IAAIgD,WAAW,CAACyC,MAAM,GAAG,CAAC,IAAIvC,iBAAiB,GAAGF,WAAW,CAACyC,MAAM,EAAE;MACpEhE,iBAAiB,CAACuB,WAAW,CAACE,iBAAiB,CAAC,CAAC;IACnD;EACF,CAAC,EAAE,CAACA,iBAAiB,EAAEF,WAAW,CAAC,CAAC;;EAEpC;EACA,MAAM0C,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRvE,YAAY,CAACuE,IAAI,CAAC;MAClBrE,eAAe,CAACwE,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC,CAAC;MAC1CnE,iBAAiB,CAAC,IAAI,CAAC;MACvBoC,eAAe,CAAC,IAAI,CAAC;MACrBF,gBAAgB,CAAC,EAAE,CAAC;MACpB9B,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMoE,YAAY,GAAGA,CAAA,KAAM;IACzB1D,eAAe,CAAC,CAACD,YAAY,CAAC;IAC9B,IAAI,CAACA,YAAY,EAAE;MACjBjB,YAAY,CAAC,IAAI,CAAC;MAClBE,eAAe,CAAC,IAAI,CAAC;MACrBE,iBAAiB,CAAC,IAAI,CAAC;MACvBoC,eAAe,CAAC,IAAI,CAAC;MACrBF,gBAAgB,CAAC,EAAE,CAAC;MACpB9B,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMqE,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACpC,SAAS,CAACqB,OAAO,IAAI,CAACrB,SAAS,CAACqB,OAAO,CAACgB,MAAM,EAAE;MACnDtE,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEA,IAAI;MACFkB,iBAAiB,CAAC,EAAE,CAAC;MACrBF,gBAAgB,CAAC,CAAC,CAAC;MACnBF,cAAc,CAAC,IAAI,CAAC;MACpBd,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMuE,aAAa,GAAG,IAAIC,aAAa,CAACvC,SAAS,CAACqB,OAAO,CAACgB,MAAM,EAAE;QAChEG,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEFrC,gBAAgB,CAACkB,OAAO,GAAGiB,aAAa;MAExCA,aAAa,CAACG,eAAe,GAAIC,KAAK,IAAK;QACzC,IAAIA,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvB3D,iBAAiB,CAACsC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEmB,KAAK,CAACC,IAAI,CAAC,CAAC;QAClD;MACF,CAAC;MAEDL,aAAa,CAACO,MAAM,GAAG,MAAM;QAC3B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC/D,cAAc,EAAE;UAAEgE,IAAI,EAAE;QAAa,CAAC,CAAC;QAC7D,MAAMlB,IAAI,GAAG,IAAImB,IAAI,CAAC,CAACH,IAAI,CAAC,EAAE,kBAAkBI,IAAI,CAACC,GAAG,CAAC,CAAC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAa,CAAC,CAAC;QAC1FzF,YAAY,CAACuE,IAAI,CAAC;QAClBrE,eAAe,CAACwE,GAAG,CAACC,eAAe,CAACY,IAAI,CAAC,CAAC;QAC1CjE,cAAc,CAAC,KAAK,CAAC;QACrBE,gBAAgB,CAAC,CAAC,CAAC;MACrB,CAAC;MAEDuD,aAAa,CAACc,KAAK,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOtF,KAAK,EAAE;MACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAACuF,OAAO,CAAC;MACvDxE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM2C,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIrB,gBAAgB,CAACkB,OAAO,IAAIzC,WAAW,EAAE;MAC3CuB,gBAAgB,CAACkB,OAAO,CAACiC,IAAI,CAAC,CAAC;MAC/BzE,cAAc,CAAC,KAAK,CAAC;MACrBE,gBAAgB,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMwE,uBAAuB,GAAGA,CAAA,KAAM;IACpC5E,oBAAoB,CAAC4C,IAAI,IAAIA,IAAI,KAAK,aAAa,GAAG,MAAM,GAAG,aAAa,CAAC;EAC/E,CAAC;;EAED;EACA,MAAMiC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,OAAQlF,WAAW,KAAK,OAAO,IAAIhB,SAAS,IACpCgB,WAAW,KAAK,QAAQ,IAAIhB,SAAU;EAChD,CAAC;;EAED;EACA,MAAMmG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACD,oBAAoB,CAAC,CAAC,EAAE;MAC3BzF,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,eAAe,CAAC,IAAI,CAAC;IACrBE,aAAa,CAAC,KAAK,CAAC;IACpBoB,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,KAAK,CAAC;IACnBN,cAAc,CAAC,EAAE,CAAC;IAClBE,oBAAoB,CAAC,CAAC,CAAC;IACvBM,qBAAqB,CAAC,CAAC,CAAC;IACxBE,gBAAgB,CAAC,EAAE,CAAC;IACpBlC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBoC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAG;;IAEzB,IAAI;MACF,MAAM2D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEtG,SAAS,CAAC;MACnCoG,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAExG,aAAa,CAAC;MAC/CsG,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAExF,WAAW,CAAC;MAE3CgD,OAAO,CAACyC,GAAG,CAAC,uCAAuC,EAAEzG,aAAa,CAAC;;MAEnE;MACA,MAAM0G,MAAM,GAAG,4BAA4B;;MAE3C;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,MAAM,EAAE;QACnCG,MAAM,EAAE,MAAM;QACdC,IAAI,EAAER;MACR,CAAC,CAAC;MAEF,IAAI,CAACK,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBL,QAAQ,CAACM,MAAM,EAAE,CAAC;MAC3D;;MAEA;MACA,MAAMC,MAAM,GAAGP,QAAQ,CAACG,IAAI,CAACK,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;MAEjC,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,OAAO,IAAI,EAAE;YACX,MAAM;cAAEC,IAAI;cAAElE;YAAM,CAAC,GAAG,MAAM6D,MAAM,CAACM,IAAI,CAAC,CAAC;YAC3C,IAAID,IAAI,EAAE;cACRvD,OAAO,CAACyC,GAAG,CAAC,wBAAwB,CAAC;cACrC5F,eAAe,CAAC,KAAK,CAAC;cACtBJ,UAAU,CAAC,KAAK,CAAC;cACjB0B,cAAc,CAAC,KAAK,CAAC;cACrB;YACF;YAEA,MAAMsF,KAAK,GAAGL,OAAO,CAACM,MAAM,CAACrE,KAAK,EAAE;cAAE4B,MAAM,EAAE;YAAK,CAAC,CAAC;YACrD,MAAM0C,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC;YAE/B,KAAK,MAAMC,IAAI,IAAIF,KAAK,EAAE;cACxB,IAAIE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC7B,IAAI;kBACF,MAAMvC,IAAI,GAAGwC,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC;kBAC1CjE,OAAO,CAACyC,GAAG,CAAC,oBAAoB,EAAE;oBAChCyB,QAAQ,EAAE,CAAC,CAAC3C,IAAI,CAAC4C,KAAK;oBACtBC,WAAW,EAAE7C,IAAI,CAAC4C,KAAK,GAAG5C,IAAI,CAAC4C,KAAK,CAAC5D,MAAM,GAAG,CAAC;oBAC/C8D,QAAQ,EAAE9C,IAAI,CAAC8C,QAAQ;oBACvBC,UAAU,EAAE/C,IAAI,CAACgD,WAAW;oBAC5BC,UAAU,EAAEjD,IAAI,CAACiD,UAAU,GAAGjD,IAAI,CAACiD,UAAU,CAACjE,MAAM,GAAG;kBACzD,CAAC,CAAC;kBAEF,IAAIgB,IAAI,CAACkD,OAAO,KAAK,KAAK,EAAE;oBAC1B9H,QAAQ,CAAC4E,IAAI,CAACU,OAAO,IAAI,yBAAyB,CAAC;oBACnDpF,eAAe,CAAC,KAAK,CAAC;oBACtBJ,UAAU,CAAC,KAAK,CAAC;oBACjB0B,cAAc,CAAC,KAAK,CAAC;oBACrB;kBACF;kBAEA,IAAIoD,IAAI,CAAC4C,KAAK,IAAI,OAAO5C,IAAI,CAAC4C,KAAK,KAAK,QAAQ,IAAI5C,IAAI,CAAC4C,KAAK,CAAC5D,MAAM,GAAG,IAAI,EAAE;oBAC5E;oBACAxC,cAAc,CAACoC,IAAI,IAAI;sBACrB,MAAMuE,SAAS,GAAG,CAAC,GAAGvE,IAAI,EAAEoB,IAAI,CAAC4C,KAAK,CAAC;;sBAEvC;sBACAlG,oBAAoB,CAACyG,SAAS,CAACnE,MAAM,GAAG,CAAC,CAAC;;sBAE1C;sBACA,IAAImE,SAAS,CAACnE,MAAM,KAAK,CAAC,EAAE;wBAC1BpC,cAAc,CAAC,KAAK,CAAC;wBACrBE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;wBACrB9B,iBAAiB,CAACgF,IAAI,CAAC4C,KAAK,CAAC,CAAC,CAAC;sBACjC;sBAEA,OAAOO,SAAS;oBAClB,CAAC,CAAC;;oBAEF;oBACAnI,iBAAiB,CAACgF,IAAI,CAAC4C,KAAK,CAAC;kBAC/B;;kBAEA;kBACA,IAAI5C,IAAI,CAAC8C,QAAQ,KAAKM,SAAS,EAAE;oBAC/BpG,qBAAqB,CAACgD,IAAI,CAAC8C,QAAQ,CAAC;oBACpCrE,OAAO,CAACyC,GAAG,CAAC,wBAAwBlB,IAAI,CAAC8C,QAAQ,CAACvE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;kBAClE;;kBAEA;kBACA,IAAIyB,IAAI,CAACiD,UAAU,IAAIjD,IAAI,CAACiD,UAAU,CAACjE,MAAM,GAAG,CAAC,EAAE;oBACjD9B,gBAAgB,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGoB,IAAI,CAACiD,UAAU,CAAC,CAAC;kBACzD;;kBAEA;kBACA,IAAIjD,IAAI,CAACqD,cAAc,EAAE;oBACvBjG,eAAe,CAAC4C,IAAI,CAAC;oBACrB9C,gBAAgB,CAAC8C,IAAI,CAACqD,cAAc,CAAC;oBACrC/H,eAAe,CAAC,KAAK,CAAC;oBACtBJ,UAAU,CAAC,KAAK,CAAC;oBACjB0B,cAAc,CAAC,KAAK,CAAC;oBACrBI,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;;oBAE5B;oBACAN,oBAAoB,CAAC,CAAC,CAAC;oBACvBI,YAAY,CAAC,KAAK,CAAC;oBAEnB2B,OAAO,CAACyC,GAAG,CAAC,4BAA4B,CAAC;oBACzCzC,OAAO,CAACyC,GAAG,CAAC,4BAA4BlB,IAAI,CAACsD,uBAAuB,IAAItD,IAAI,CAACqD,cAAc,CAACrE,MAAM,EAAE,CAAC;oBACrGP,OAAO,CAACyC,GAAG,CAAC,2BAA2BlB,IAAI,CAACuD,sBAAsB,IAAIvD,IAAI,CAACqD,cAAc,CAACrE,MAAM,EAAE,CAAC;oBACnGP,OAAO,CAACyC,GAAG,CAAC,2BAA2B3E,WAAW,CAACyC,MAAM,EAAE,CAAC;oBAC5D;kBACF;;kBAEA;kBACA,IAAIgB,IAAI,CAACwD,GAAG,EAAE;oBACZ/E,OAAO,CAACyC,GAAG,CAAC,qCAAqC,CAAC;oBAClD5F,eAAe,CAAC,KAAK,CAAC;oBACtBJ,UAAU,CAAC,KAAK,CAAC;oBACjB0B,cAAc,CAAC,KAAK,CAAC;oBACrB;kBACF;gBACF,CAAC,CAAC,OAAO6G,UAAU,EAAE;kBACnBhF,OAAO,CAACiF,IAAI,CAAC,yBAAyB,EAAED,UAAU,CAAC;gBACrD;cACF;YACF;UACF;QACF,CAAC,CAAC,OAAOE,WAAW,EAAE;UACpBlF,OAAO,CAACtD,KAAK,CAAC,0BAA0B,EAAEwI,WAAW,CAAC;UACtDvI,QAAQ,CAAC,+BAA+B,CAAC;UACzCE,eAAe,CAAC,KAAK,CAAC;UACtBJ,UAAU,CAAC,KAAK,CAAC;UACjB0B,cAAc,CAAC,KAAK,CAAC;QACvB,CAAC,SAAS;UACR;UACA,IAAI+E,MAAM,EAAE;YACV,IAAI;cACFA,MAAM,CAACiC,WAAW,CAAC,CAAC;YACtB,CAAC,CAAC,OAAO1E,CAAC,EAAE;cACVT,OAAO,CAACiF,IAAI,CAAC,8BAA8B,EAAExE,CAAC,CAAC;YACjD;UACF;QACF;MACF,CAAC;MAED6C,aAAa,CAAC,CAAC;IAEjB,CAAC,CAAC,OAAO5G,KAAK,EAAE;MACdsD,OAAO,CAACtD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACD,KAAK,CAACuF,OAAO,IAAI,yBAAyB,CAAC;MACpDxF,UAAU,CAAC,KAAK,CAAC;MACjBI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMuI,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAM7J,KAAK,CAAC8J,IAAI,CAAC,qCAAqC,CAAC;MAEvDxI,eAAe,CAAC,KAAK,CAAC;MACtBE,aAAa,CAAC,IAAI,CAAC;MACnBoB,cAAc,CAAC,KAAK,CAAC;MACrBE,YAAY,CAAC,KAAK,CAAC;MACnB5B,UAAU,CAAC,KAAK,CAAC;MACjBE,QAAQ,CAAC,0BAA0B,CAAC;IACtC,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdsD,OAAO,CAACtD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,2BAA2B,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAM2I,WAAW,GAAGA,CAAA,KAAM;IACxBnJ,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC,IAAI,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IACvBoC,eAAe,CAAC,IAAI,CAAC;IACrBF,gBAAgB,CAAC,EAAE,CAAC;IACpBV,cAAc,CAAC,EAAE,CAAC;IAClBE,oBAAoB,CAAC,CAAC,CAAC;IACvBpB,eAAe,CAAC,KAAK,CAAC;IACtBE,aAAa,CAAC,KAAK,CAAC;IACpBoB,cAAc,CAAC,KAAK,CAAC;IACrBE,YAAY,CAAC,KAAK,CAAC;IACnBE,qBAAqB,CAAC,CAAC,CAAC;IACxB5B,QAAQ,CAAC,EAAE,CAAC;IACZV,gBAAgB,CAAC,KAAK,CAAC;IAEvB,IAAI4C,YAAY,CAACoB,OAAO,EAAE;MACxBpB,YAAY,CAACoB,OAAO,CAACZ,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMkG,eAAe,GAAGA,CAAA,KAAMlH,YAAY,CAAC,CAACD,SAAS,CAAC;EACtD,MAAMoH,YAAY,GAAGA,CAAA,KAAMvH,oBAAoB,CAACwH,IAAI,CAACC,GAAG,CAAC1H,iBAAiB,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACnF,MAAM2H,aAAa,GAAGA,CAAA,KAAM1H,oBAAoB,CAACwH,IAAI,CAACG,GAAG,CAAC5H,iBAAiB,GAAG,CAAC,EAAEF,WAAW,CAACyC,MAAM,GAAG,CAAC,CAAC,CAAC;;EAEzG;EACA,MAAMsF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClBtH,aAAa,CAACuH,OAAO,CAACC,GAAG,IAAI;MAC3BF,OAAO,CAACE,GAAG,CAACpE,IAAI,CAAC,GAAG,CAACkE,OAAO,CAACE,GAAG,CAACpE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAClD,CAAC,CAAC;IACF,OAAOkE,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIvH,YAAY,EAAE;MAChB,OAAO;QACLwH,gBAAgB,EAAExH,YAAY,CAACmG,uBAAuB,IAAIrG,aAAa,CAAC+B,MAAM;QAC9E4F,eAAe,EAAEzH,YAAY,CAACoG,sBAAsB,IAAItG,aAAa,CAAC+B,MAAM;QAC5E6F,iBAAiB,EAAE,CAAC1H,YAAY,CAACoG,sBAAsB,IAAItG,aAAa,CAAC+B,MAAM,KAAK7B,YAAY,CAACmG,uBAAuB,IAAIrG,aAAa,CAAC+B,MAAM;MAClJ,CAAC;IACH;IACA,OAAO;MACL2F,gBAAgB,EAAE1H,aAAa,CAAC+B,MAAM;MACtC4F,eAAe,EAAE3H,aAAa,CAAC+B,MAAM;MACrC6F,iBAAiB,EAAE;IACrB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGd,IAAI,CAACe,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMG,IAAI,GAAGH,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;EAED,oBACEhL,OAAA;IAAKiL,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrClL,OAAA,CAACN,GAAG;MAAAwL,QAAA,gBACFlL,OAAA,CAACL,GAAG;QAACwL,EAAE,EAAE,CAAE;QAAAD,QAAA,eACTlL,OAAA,CAACZ,IAAI;UAAC6L,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACpBlL,OAAA,CAACZ,IAAI,CAACgM,MAAM;YAACH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAC5ClL,OAAA;cAAIiL,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACdxL,OAAA,CAACZ,IAAI,CAACqM,IAAI;YAAAP,QAAA,GACPnK,KAAK,iBACJf,OAAA,CAACT,KAAK;cAACmM,OAAO,EAAC,QAAQ;cAACT,SAAS,EAAC,MAAM;cAAAC,QAAA,EACrCnK;YAAK;cAAAsK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR,eAGDxL,OAAA,CAACV,IAAI,CAACqM,KAAK;cAACV,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BlL,OAAA,CAACV,IAAI,CAACsM,KAAK;gBAAAV,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxCxL,OAAA,CAACV,IAAI,CAACuM,MAAM;gBACVnI,KAAK,EAAErD,aAAc;gBACrByL,QAAQ,EAAGhH,CAAC,IAAKxE,gBAAgB,CAACwE,CAAC,CAACE,MAAM,CAACtB,KAAK,CAAE;gBAClDqI,QAAQ,EAAE9K,YAAa;gBAAAiK,QAAA,EAEtBzH,YAAY,CAACuI,GAAG,CAACC,MAAM,iBACtBjM,OAAA;kBAA2B0D,KAAK,EAAEuI,MAAM,CAACvI,KAAM;kBAAAwH,QAAA,EAC5Ce,MAAM,CAACtI;gBAAK,GADFsI,MAAM,CAACvI,KAAK;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGbxL,OAAA,CAACV,IAAI,CAACqM,KAAK;cAACV,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BlL,OAAA,CAACV,IAAI,CAACsM,KAAK;gBAAAV,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCxL,OAAA,CAACV,IAAI,CAACuM,MAAM;gBACVnI,KAAK,EAAEnC,WAAY;gBACnBuK,QAAQ,EAAGhH,CAAC,IAAKtD,cAAc,CAACsD,CAAC,CAACE,MAAM,CAACtB,KAAK,CAAE;gBAChDqI,QAAQ,EAAE9K,YAAa;gBAAAiK,QAAA,gBAEvBlL,OAAA;kBAAQ0D,KAAK,EAAC,OAAO;kBAAAwH,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CxL,OAAA;kBAAQ0D,KAAK,EAAC,QAAQ;kBAAAwH,QAAA,EAAC;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAGZjK,WAAW,KAAK,OAAO,iBACtBvB,OAAA,CAACV,IAAI,CAACqM,KAAK;cAACV,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BlL,OAAA,CAACV,IAAI,CAACsM,KAAK;gBAAAV,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCxL,OAAA,CAACV,IAAI,CAAC4M,OAAO;gBACXjG,IAAI,EAAC,MAAM;gBACXkG,MAAM,EAAC,SAAS;gBAChBL,QAAQ,EAAEjH,iBAAkB;gBAC5BuH,GAAG,EAAElJ,YAAa;gBAClB6I,QAAQ,EAAE9K;cAAa;gBAAAoK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,EACD/K,YAAY,iBACXT,OAAA;gBAAKiL,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBlL,OAAA;kBACEqM,GAAG,EAAE5L,YAAa;kBAClB6L,QAAQ;kBACRrB,SAAS,EAAC,eAAe;kBACzBsB,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAQ;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CACb,EAGAjK,WAAW,KAAK,QAAQ,iBACvBvB,OAAA;cAAKiL,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBlL,OAAA;gBAAKiL,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClL,OAAA,CAACX,MAAM;kBACLqM,OAAO,EAAEjK,YAAY,GAAG,QAAQ,GAAG,MAAO;kBAC1CgL,OAAO,EAAErH,YAAa;kBACtB2G,QAAQ,EAAE9K,YAAa;kBAAAiK,QAAA,EAEtBzJ,YAAY,GAAG,aAAa,GAAG;gBAAc;kBAAA4J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,EACRnI,QAAQ,IAAI5B,YAAY,iBACvBzB,OAAA,CAACX,MAAM;kBACLqM,OAAO,EAAC,mBAAmB;kBAC3Be,OAAO,EAAEjG,uBAAwB;kBACjCX,IAAI,EAAC,IAAI;kBAAAqF,QAAA,EACV;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAEL/J,YAAY,iBACXzB,OAAA;gBAAKiL,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlL,OAAA,CAACH,MAAM;kBACL6M,KAAK,EAAE,KAAM;kBACbN,GAAG,EAAEnJ,SAAU;kBACf0J,gBAAgB,EAAC,YAAY;kBAC7BC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,MAAM;kBACbC,gBAAgB,EAAE;oBAChBF,KAAK,EAAE,GAAG;oBACVC,MAAM,EAAE,GAAG;oBACXE,UAAU,EAAEpL;kBACd;gBAAE;kBAAA0J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEFxL,OAAA;kBAAKiL,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClB,CAACrJ,WAAW,gBACX7B,OAAA,CAACX,MAAM;oBACLqM,OAAO,EAAC,SAAS;oBACjBe,OAAO,EAAEpH,oBAAqB;oBAC9B0G,QAAQ,EAAE9K,YAAa;oBAAAiK,QAAA,EACxB;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gBAETxL,OAAA;oBAAKiL,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9ClL,OAAA,CAACX,MAAM;sBACLqM,OAAO,EAAC,QAAQ;sBAChBe,OAAO,EAAEhI,mBAAoB;sBAAAyG,QAAA,EAC9B;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTxL,OAAA;sBAAMiL,SAAS,EAAC,aAAa;sBAAAC,QAAA,GAAC,aACjB,EAACR,UAAU,CAAC3I,aAAa,CAAC,EAAC,KAAG,EAAC2I,UAAU,CAAClH,kBAAkB,CAAC;oBAAA;sBAAA6H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA/K,YAAY,iBACXT,OAAA;gBAAKiL,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBlL,OAAA;kBACEqM,GAAG,EAAE5L,YAAa;kBAClB6L,QAAQ;kBACRrB,SAAS,EAAC,eAAe;kBACzBsB,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAQ;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eAGDxL,OAAA;cAAKiL,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlL,OAAA,CAACX,MAAM;gBACLqM,OAAO,EAAC,SAAS;gBACjBe,OAAO,EAAE/F,aAAc;gBACvBqF,QAAQ,EAAE,CAACtF,oBAAoB,CAAC,CAAC,IAAIxF,YAAa;gBAAAiK,QAAA,EAEjDrK,OAAO,gBACNb,OAAA,CAAAE,SAAA;kBAAAgL,QAAA,gBACElL,OAAA,CAACR,OAAO;oBAACqG,IAAI,EAAC,IAAI;oBAACoF,SAAS,EAAC;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAExC;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,EAERvK,YAAY,iBACXjB,OAAA,CAACX,MAAM;gBACLqM,OAAO,EAAC,SAAS;gBACjBe,OAAO,EAAEhD,oBAAqB;gBAAAyB,QAAA,EAC/B;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,eAEDxL,OAAA,CAACX,MAAM;gBACLqM,OAAO,EAAC,WAAW;gBACnBe,OAAO,EAAE9C,WAAY;gBACrBoC,QAAQ,EAAE9K,YAAa;gBAAAiK,QAAA,EACxB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGLvK,YAAY,iBACXjB,OAAA;cAAKiL,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBlL,OAAA;gBAAKiL,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7ClL,OAAA;kBAAAkL,QAAA,EAAM;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjCxL,OAAA;kBAAAkL,QAAA,GAAOvI,kBAAkB,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAkH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNxL,OAAA;gBAAKiL,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BlL,OAAA;kBACEiL,SAAS,EAAC,cAAc;kBACxB+B,IAAI,EAAC,aAAa;kBAClBT,KAAK,EAAE;oBAAEK,KAAK,EAAE,GAAGjK,kBAAkB;kBAAI;gBAAE;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENxL,OAAA,CAACL,GAAG;QAACwL,EAAE,EAAE,CAAE;QAAAD,QAAA,GAERvK,cAAc,iBACbX,OAAA,CAACZ,IAAI;UAAC6L,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACpBlL,OAAA,CAACZ,IAAI,CAACgM,MAAM;YAACH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAC5ClL,OAAA;cAAIiL,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACdxL,OAAA,CAACZ,IAAI,CAACqM,IAAI;YAAAP,QAAA,eACRlL,OAAA;cAAKiL,SAAS,EAAC,2BAA2B;cAAAC,QAAA,GACvC3I,WAAW,iBACVvC,OAAA;gBAAKiL,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjClL,OAAA,CAACR,OAAO;kBAACyN,SAAS,EAAC;gBAAQ;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9BxL,OAAA;kBAAMiL,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CACN,eAEDxL,OAAA;gBACEqM,GAAG,EAAE1L,cAAc,CAACwH,UAAU,CAAC,OAAO,CAAC,GAAGxH,cAAc,GAAG,0BAA0BA,cAAc,EAAG;gBACtGuM,GAAG,EAAC,iBAAiB;gBACrBX,KAAK,EAAE;kBAAEY,QAAQ,EAAE;gBAAO;cAAE;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EAGDrJ,WAAW,CAACyC,MAAM,GAAG,CAAC,iBACrB5E,OAAA;gBAAKiL,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClClL,OAAA;kBAAKiL,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClL,OAAA,CAACX,MAAM;oBAACwG,IAAI,EAAC,IAAI;oBAAC6F,OAAO,EAAC,iBAAiB;oBAACe,OAAO,EAAE5C,YAAa;oBAAAqB,QAAA,EAAC;kBAEnE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTxL,OAAA,CAACX,MAAM;oBAACwG,IAAI,EAAC,IAAI;oBAAC6F,OAAO,EAAC,iBAAiB;oBAACe,OAAO,EAAE7C,eAAgB;oBAAAsB,QAAA,EAClEzI,SAAS,GAAG,IAAI,GAAG;kBAAI;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACTxL,OAAA,CAACX,MAAM;oBAACwG,IAAI,EAAC,IAAI;oBAAC6F,OAAO,EAAC,iBAAiB;oBAACe,OAAO,EAAEzC,aAAc;oBAAAkB,QAAA,EAAC;kBAEpE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAENxL,OAAA,CAACV,IAAI,CAAC8N,KAAK;kBACTnD,GAAG,EAAE,CAAE;kBACPF,GAAG,EAAE5H,WAAW,CAACyC,MAAM,GAAG,CAAE;kBAC5BlB,KAAK,EAAErB,iBAAkB;kBACzByJ,QAAQ,EAAGhH,CAAC,IAAKxC,oBAAoB,CAAC+K,MAAM,CAACvI,CAAC,CAACE,MAAM,CAACtB,KAAK,CAAC;gBAAE;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC,eAEFxL,OAAA;kBAAKiL,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,GAAC,QACtC,EAAC7I,iBAAiB,GAAG,CAAC,EAAC,MAAI,EAACF,WAAW,CAACyC,MAAM;gBAAA;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACP,EAGA3I,aAAa,CAAC+B,MAAM,GAAG,CAAC,iBACvB5E,OAAA,CAACZ,IAAI;UAAA8L,QAAA,gBACHlL,OAAA,CAACZ,IAAI,CAACgM,MAAM;YAACH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACzClL,OAAA;cAAIiL,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACdxL,OAAA,CAACZ,IAAI,CAACqM,IAAI;YAAAP,QAAA,gBAERlL,OAAA;cAAKiL,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrClL,OAAA;gBAAAkL,QAAA,EAAI;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BxL,OAAA;gBAAKiL,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAClBoC,MAAM,CAACC,OAAO,CAACrD,mBAAmB,CAAC,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAAC,CAAC/F,IAAI,EAAEuH,KAAK,CAAC,kBACvDxN,OAAA;kBAAiBiL,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GACjDjF,IAAI,EAAC,IAAE,EAACuH,KAAK;gBAAA,GADLvH,IAAI;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNxL,OAAA;gBAAKiL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7BlL,OAAA;kBAAOiL,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBAC3BlL,OAAA;oBAAAkL,QAAA,EAAQ;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,GAAG,eACrCxL,OAAA;oBAAMiL,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,UAC9B,EAACZ,gBAAgB,CAAC,CAAC,CAACC,gBAAgB;kBAAA;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACPxL,OAAA;oBAAMiL,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,GAAC,gBACrB,EAACZ,gBAAgB,CAAC,CAAC,CAACE,eAAe;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACPxL,OAAA;oBAAMiL,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,sBACb,EAACZ,gBAAgB,CAAC,CAAC,CAACG,iBAAiB;kBAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL,CAAC,MAAM;cACN,MAAMiC,iBAAiB,GAAG5K,aAAa,CAAC6K,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1H,IAAI,KAAK,SAAS,CAAC;cACzE,MAAM2H,eAAe,GAAG/K,aAAa,CAAC6K,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1H,IAAI,CAAC4H,QAAQ,CAAC,OAAO,CAAC,CAAC;cAC3E,MAAMC,cAAc,GAAGjL,aAAa,CAAC6K,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1H,IAAI,CAAC4H,QAAQ,CAAC,MAAM,CAAC,CAAC;cAEzE,oBACE7N,OAAA;gBAAAkL,QAAA,GAEG,CAAC7K,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,UAAU,kBACvDL,OAAA;kBAAKiL,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3ClL,OAAA;oBAAIiL,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBACzBlL,OAAA;sBAAMiL,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,uBACf,EAACiC,iBAAiB,CAAC7I,MAAM;kBAAA;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,EACJiC,iBAAiB,CAAC7I,MAAM,GAAG,CAAC,gBAC3B5E,OAAA;oBAAKiL,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxClL,OAAA,CAACP,KAAK;sBAACsO,OAAO;sBAACC,QAAQ;sBAACC,KAAK;sBAACpI,IAAI,EAAC,IAAI;sBAAAqF,QAAA,gBACrClL,OAAA;wBAAAkL,QAAA,eACElL,OAAA;0BAAAkL,QAAA,gBACElL,OAAA;4BAAAkL,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAK;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACdxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAa;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACtBxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAW;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACpBxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAc;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACvBxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACrBxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRxL,OAAA;wBAAAkL,QAAA,EACGuC,iBAAiB,CAACzB,GAAG,CAAC,CAACkC,SAAS,EAAEC,KAAK,kBACtCnO,OAAA;0BAAAkL,QAAA,gBACElL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACE,QAAQ,IAAID,KAAK,GAAG;0BAAC;4BAAA9C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1CxL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAAC1F;0BAAK;4BAAA6C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1BxL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACG,SAAS,GAAGH,SAAS,CAACG,SAAS,CAAClK,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACvExL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACI,UAAU,GAAGJ,SAAS,CAACI,UAAU,CAACnK,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACzExL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACK,QAAQ,GAAGL,SAAS,CAACK,QAAQ,CAACpK,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrExL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACM,QAAQ,GAAGN,SAAS,CAACM,QAAQ,CAACrK,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrExL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACO,SAAS,GAAGP,SAAS,CAACO,SAAS,CAACtK,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACvExL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACQ,YAAY,GAAGR,SAAS,CAACQ,YAAY,CAACvK,OAAO,CAAC,CAAC,CAAC,GAAI+J,SAAS,CAACS,QAAQ,GAAGT,SAAS,CAACS,QAAQ,CAACxK,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAM;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpIxL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACU,MAAM,GAAGV,SAAS,CAACU,MAAM,CAACzK,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACjExL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACW,YAAY,IAAI;0BAAK;4BAAAxD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GAVnC2C,KAAK;0BAAA9C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAWV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENxL,OAAA;oBAAKiL,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC9D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EAGA,CAACnL,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,kBAAkB,kBAC/DL,OAAA;kBAAKiL,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzClL,OAAA;oBAAIiL,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC1BlL,OAAA;sBAAMiL,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,qBAChB,EAACoC,eAAe,CAAChJ,MAAM;kBAAA;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,EACJoC,eAAe,CAAChJ,MAAM,GAAG,CAAC,gBACzB5E,OAAA;oBAAKiL,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxClL,OAAA,CAACP,KAAK;sBAACsO,OAAO;sBAACC,QAAQ;sBAACC,KAAK;sBAACpI,IAAI,EAAC,IAAI;sBAAAqF,QAAA,gBACrClL,OAAA;wBAAAkL,QAAA,eACElL,OAAA;0BAAAkL,QAAA,gBACElL,OAAA;4BAAAkL,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAI;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACbxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRxL,OAAA;wBAAAkL,QAAA,EACG0C,eAAe,CAAC5B,GAAG,CAAC,CAACkC,SAAS,EAAEC,KAAK,kBACpCnO,OAAA;0BAAAkL,QAAA,gBACElL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACE,QAAQ,IAAID,KAAK,GAAG;0BAAC;4BAAA9C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1CxL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACjI;0BAAI;4BAAAoF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACzBxL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACK,QAAQ,GAAGL,SAAS,CAACK,QAAQ,CAACpK,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrExL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACY,UAAU,IAAI;0BAAK;4BAAAzD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GAJjC2C,KAAK;0BAAA9C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAKV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENxL,OAAA;oBAAKiL,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC5D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EAGA,CAACnL,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,OAAO,kBACpDL,OAAA;kBAAKiL,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxClL,OAAA;oBAAIiL,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC1BlL,OAAA;sBAAMiL,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,oBACjB,EAACsC,cAAc,CAAClJ,MAAM;kBAAA;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACJsC,cAAc,CAAClJ,MAAM,GAAG,CAAC,gBACxB5E,OAAA;oBAAKiL,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxClL,OAAA,CAACP,KAAK;sBAACsO,OAAO;sBAACC,QAAQ;sBAACC,KAAK;sBAACpI,IAAI,EAAC,IAAI;sBAAAqF,QAAA,gBACrClL,OAAA;wBAAAkL,QAAA,eACElL,OAAA;0BAAAkL,QAAA,gBACElL,OAAA;4BAAAkL,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAI;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACbxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAS;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAClBxL,OAAA;4BAAAkL,QAAA,EAAI;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRxL,OAAA;wBAAAkL,QAAA,EACG4C,cAAc,CAAC9B,GAAG,CAAC,CAACkC,SAAS,EAAEC,KAAK,kBACnCnO,OAAA;0BAAAkL,QAAA,gBACElL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACE,QAAQ,IAAID,KAAK,GAAG;0BAAC;4BAAA9C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1CxL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACa,SAAS,IAAI;0BAAe;4BAAA1D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACjDxL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACc,SAAS,IAAId,SAAS,CAACjI;0BAAI;4BAAAoF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAChDxL,OAAA;4BAAAkL,QAAA,EAAKgD,SAAS,CAACe,QAAQ,GAAGf,SAAS,CAACe,QAAQ,CAAC9K,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GAJ9D2C,KAAK;0BAAA9C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAKV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENxL,OAAA;oBAAKiL,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC3D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAEV,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpL,EAAA,CA32BID,oBAAoB;EAAA,QA8BHL,aAAa;AAAA;AAAAoP,EAAA,GA9B9B/O,oBAAoB;AA62B1B,eAAeA,oBAAoB;AAAC,IAAA+O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}