#!/usr/bin/env python3
"""
Test script to verify depth calculation functionality
"""

import numpy as np
import cv2
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.models import calculate_real_depth

def create_test_data():
    """Create synthetic test data to verify depth calculation"""
    
    # Create a synthetic depth map (100x100)
    depth_map = np.ones((100, 100), dtype=np.uint8) * 150  # Background at 150
    
    # Create a "pothole" region (lower values = deeper in MiDaS)
    # Pothole in center (40x40 pixels) with depth values around 100
    depth_map[30:70, 30:70] = 100  # Pothole region
    depth_map[40:60, 40:60] = 80   # Deeper center
    depth_map[45:55, 45:55] = 60   # Deepest center
    
    # Create corresponding binary mask
    binary_mask = np.zeros((100, 100), dtype=np.uint8)
    binary_mask[30:70, 30:70] = 255  # Mark pothole region
    
    return binary_mask, depth_map

def test_depth_calculation():
    """Test the depth calculation function"""
    
    print("🧪 DEPTH TEST: Creating synthetic test data...")
    binary_mask, depth_map = create_test_data()
    
    print(f"📊 TEST DATA: binary_mask shape: {binary_mask.shape}, unique values: {np.unique(binary_mask)}")
    print(f"📊 TEST DATA: depth_map shape: {depth_map.shape}, range: [{np.min(depth_map)}, {np.max(depth_map)}]")
    print(f"📊 TEST DATA: Pothole pixels: {np.sum(binary_mask == 255)}")
    
    # Test with different calibration factors
    calibration_factors = [0.1, 0.5, 1.0, 2.0, 5.0]
    
    for factor in calibration_factors:
        print(f"\n🔬 TESTING with calibration_factor = {factor}")
        
        try:
            result = calculate_real_depth(binary_mask, depth_map, calibration_factor=factor)
            print(f"✅ RESULT: {result}")
            
            # Check if we got realistic values (not defaults)
            if result['max_depth_cm'] != 5.0:
                print(f"✅ SUCCESS: Got dynamic depth calculation!")
            else:
                print(f"⚠️ WARNING: Got default value, may indicate calculation issue")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    # Test edge cases
    print(f"\n🔬 TESTING edge cases...")
    
    # Test with empty mask
    empty_mask = np.zeros((100, 100), dtype=np.uint8)
    result = calculate_real_depth(empty_mask, depth_map, calibration_factor=1.0)
    print(f"Empty mask result: {result}")
    
    # Test with uniform depth map
    uniform_depth = np.ones((100, 100), dtype=np.uint8) * 128
    result = calculate_real_depth(binary_mask, uniform_depth, calibration_factor=1.0)
    print(f"Uniform depth result: {result}")

if __name__ == "__main__":
    test_depth_calculation()
