{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTA_GIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\Pavement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Container, Card, Button, Form, Tabs, Tab, Alert, Spinner, OverlayTrigger, Popover, Modal } from 'react-bootstrap';\nimport axios from 'axios';\nimport Webcam from 'react-webcam';\nimport './Pavement.css';\nimport useResponsive from '../hooks/useResponsive';\nimport VideoDefectDetection from '../components/VideoDefectDetection';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Pavement = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('detection');\n  const [detectionType, setDetectionType] = useState('all');\n  const [imageFiles, setImageFiles] = useState([]);\n  const [imagePreviewsMap, setImagePreviewsMap] = useState({});\n  const [imageLocationMap, setImageLocationMap] = useState({});\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [processedImage, setProcessedImage] = useState(null);\n  const [results, setResults] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [cameraActive, setCameraActive] = useState(false);\n  const [coordinates, setCoordinates] = useState('Not Available');\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\n  const [locationPermission, setLocationPermission] = useState('unknown');\n  const [locationError, setLocationError] = useState('');\n  const [locationLoading, setLocationLoading] = useState(false);\n\n  // Add state for batch processing results\n  const [batchResults, setBatchResults] = useState([]);\n  const [batchProcessing, setBatchProcessing] = useState(false);\n  const [processedCount, setProcessedCount] = useState(0);\n\n  // Add state for classification error modal\n  const [showClassificationModal, setShowClassificationModal] = useState(false);\n  const [classificationError, setClassificationError] = useState('');\n  const [totalToProcess, setTotalToProcess] = useState(0);\n\n  // Add state for auto-navigation through results\n  const [autoNavigationActive, setAutoNavigationActive] = useState(false);\n  const [autoNavigationIndex, setAutoNavigationIndex] = useState(0);\n  const autoNavigationRef = useRef(null);\n  const webcamRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const {\n    isMobile\n  } = useResponsive();\n\n  // Create the popover content\n  const reminderPopover = /*#__PURE__*/_jsxDEV(Popover, {\n    id: \"reminder-popover\",\n    style: {\n      maxWidth: '300px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Popover.Header, {\n      as: \"h3\",\n      children: \"\\uD83D\\uDCF8 Image Upload Guidelines\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Popover.Body, {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          marginBottom: '10px'\n        },\n        children: \"Please ensure your uploaded images are:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          marginBottom: '0',\n          paddingLeft: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Focused directly on the road surface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Well-lit and clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Showing the entire area of concern\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Taken from a reasonable distance to capture context\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n\n  // Safari-compatible geolocation permission check\n  const checkLocationPermission = async () => {\n    if (!navigator.permissions || !navigator.permissions.query) {\n      // Fallback for older browsers\n      return 'prompt';\n    }\n    try {\n      const permission = await navigator.permissions.query({\n        name: 'geolocation'\n      });\n      return permission.state;\n    } catch (err) {\n      console.warn('Permission API not supported or failed:', err);\n      return 'prompt';\n    }\n  };\n\n  // Safari-compatible geolocation request\n  const requestLocation = () => {\n    return new Promise((resolve, reject) => {\n      // Check if geolocation is supported\n      if (!navigator.geolocation) {\n        reject(new Error('Geolocation is not supported by this browser'));\n        return;\n      }\n\n      // Check if we're in a secure context (HTTPS)\n      if (!window.isSecureContext) {\n        reject(new Error('Geolocation requires a secure context (HTTPS)'));\n        return;\n      }\n      const options = {\n        enableHighAccuracy: true,\n        timeout: 15000,\n        // 15 seconds timeout\n        maximumAge: 60000 // Accept cached position up to 1 minute old\n      };\n      navigator.geolocation.getCurrentPosition(position => {\n        resolve(position);\n      }, error => {\n        let errorMessage = 'Unable to retrieve location';\n        switch (error.code) {\n          case error.PERMISSION_DENIED:\n            errorMessage = 'Location access denied. Please enable location permissions in your browser settings.';\n            break;\n          case error.POSITION_UNAVAILABLE:\n            errorMessage = 'Location information is unavailable. Please try again.';\n            break;\n          case error.TIMEOUT:\n            errorMessage = 'Location request timed out. Please try again.';\n            break;\n          default:\n            errorMessage = `Location error: ${error.message}`;\n            break;\n        }\n        reject(new Error(errorMessage));\n      }, options);\n    });\n  };\n\n  // Enhanced location handler with Safari-specific fixes\n  const handleLocationRequest = async () => {\n    setLocationLoading(true);\n    setLocationError('');\n    try {\n      // First check permission state\n      const permissionState = await checkLocationPermission();\n      setLocationPermission(permissionState);\n\n      // If permission is denied, provide user guidance\n      if (permissionState === 'denied') {\n        const errorMsg = 'Location access denied. To enable location access:\\n' + '• Safari: Settings > Privacy & Security > Location Services\\n' + '• Chrome: Settings > Privacy > Location\\n' + '• Firefox: Settings > Privacy > Location\\n' + 'Then refresh this page and try again.';\n        setLocationError(errorMsg);\n        setCoordinates('Permission Denied');\n        return;\n      }\n\n      // Request location\n      const position = await requestLocation();\n      const {\n        latitude,\n        longitude\n      } = position.coords;\n\n      // Format coordinates with better precision\n      const formattedCoords = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;\n      setCoordinates(formattedCoords);\n      setLocationPermission('granted');\n      setLocationError('');\n      console.log('Location acquired:', {\n        latitude,\n        longitude,\n        accuracy: position.coords.accuracy\n      });\n    } catch (error) {\n      console.error('Location request failed:', error);\n      setLocationError(error.message);\n      setCoordinates('Location Error');\n\n      // Update permission state based on error\n      if (error.message.includes('denied')) {\n        setLocationPermission('denied');\n      }\n    } finally {\n      setLocationLoading(false);\n    }\n  };\n\n  // Handle multiple file input change\n  const handleFileChange = e => {\n    const files = Array.from(e.target.files);\n    if (files.length > 0) {\n      setImageFiles([...imageFiles, ...files]);\n\n      // Create previews and location data for each file\n      files.forEach(file => {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n          setImagePreviewsMap(prev => ({\n            ...prev,\n            [file.name]: reader.result\n          }));\n        };\n        reader.readAsDataURL(file);\n\n        // Store location as \"Not Available\" for uploaded files\n        setImageLocationMap(prev => ({\n          ...prev,\n          [file.name]: 'Not Available'\n        }));\n      });\n\n      // Reset results\n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n    }\n  };\n\n  // Handle camera capture with location validation\n  const handleCapture = async () => {\n    const imageSrc = webcamRef.current.getScreenshot();\n    if (imageSrc) {\n      // If we don't have location data, try to get it before capturing\n      if (coordinates === 'Not Available' || coordinates === 'Location Error') {\n        await handleLocationRequest();\n      }\n      const timestamp = new Date().toISOString();\n      const filename = `camera_capture_${timestamp}.jpg`;\n      const captureCoordinates = coordinates; // Capture current coordinates\n\n      setImageFiles([...imageFiles, filename]);\n      setImagePreviewsMap(prev => ({\n        ...prev,\n        [filename]: imageSrc\n      }));\n      setImageLocationMap(prev => ({\n        ...prev,\n        [filename]: captureCoordinates\n      }));\n      setCurrentImageIndex(imageFiles.length);\n      setProcessedImage(null);\n      setResults(null);\n      setError('');\n\n      // Log capture with current coordinates\n      console.log('Photo captured with coordinates:', captureCoordinates);\n    }\n  };\n\n  // Get location data for currently selected image\n  const getCurrentImageLocation = () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      return coordinates; // Use current coordinates if no images\n    }\n    const currentFilename = Object.keys(imagePreviewsMap)[currentImageIndex];\n    return imageLocationMap[currentFilename] || 'Not Available';\n  };\n\n  // Toggle camera with improved location handling\n  const toggleCamera = async () => {\n    const newCameraState = !cameraActive;\n    setCameraActive(newCameraState);\n    if (newCameraState) {\n      // Get location when camera is activated\n      await handleLocationRequest();\n    } else {\n      // Only reset location if no images are captured\n      // This preserves location data for captured images\n      if (Object.keys(imagePreviewsMap).length === 0) {\n        setCoordinates('Not Available');\n        setLocationError('');\n        setLocationPermission('unknown');\n      }\n    }\n  };\n\n  // Toggle camera orientation (front/back) for mobile devices\n  const toggleCameraOrientation = () => {\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\n  };\n\n  // Helper function to handle classification errors\n  const handleClassificationError = errorMessage => {\n    setClassificationError(errorMessage);\n    setShowClassificationModal(true);\n    setError(''); // Clear general error since we're showing specific modal\n  };\n\n  // Process image for detection\n  const handleProcess = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      // Get user info from session storage\n      const userString = sessionStorage.getItem('user');\n      const user = userString ? JSON.parse(userString) : null;\n\n      // Get the currently selected image\n      const currentImagePreview = Object.values(imagePreviewsMap)[currentImageIndex];\n      if (!currentImagePreview) {\n        setError('No image selected for processing');\n        setLoading(false);\n        return;\n      }\n\n      // Get coordinates for the current image\n      const imageCoordinates = getCurrentImageLocation();\n\n      // Prepare request data\n      const requestData = {\n        image: currentImagePreview,\n        coordinates: imageCoordinates,\n        username: (user === null || user === void 0 ? void 0 : user.username) || 'Unknown',\n        role: (user === null || user === void 0 ? void 0 : user.role) || 'Unknown'\n      };\n\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch (detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n\n      // Make API request\n      const response = await axios.post(endpoint, requestData);\n\n      // Handle response\n      if (response.data.success) {\n        setProcessedImage(response.data.processed_image);\n        setResults(response.data);\n      } else {\n        setError(response.data.message || 'Detection failed');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'An error occurred during detection. Please try again.';\n\n      // Check if this is a classification error (no road detected)\n      if (errorMessage.includes('No road detected')) {\n        handleClassificationError(errorMessage);\n      } else {\n        setError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Add a new function to process all images\n  const handleProcessAll = async () => {\n    if (Object.keys(imagePreviewsMap).length === 0) {\n      setError('No images to process');\n      return;\n    }\n    setBatchProcessing(true);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(Object.keys(imagePreviewsMap).length);\n\n    // Get user info from session storage\n    const userString = sessionStorage.getItem('user');\n    const user = userString ? JSON.parse(userString) : null;\n    try {\n      // Determine endpoint based on detection type\n      let endpoint;\n      switch (detectionType) {\n        case 'all':\n          endpoint = '/api/pavement/detect-all';\n          break;\n        case 'potholes':\n          endpoint = '/api/pavement/detect-potholes';\n          break;\n        case 'cracks':\n          endpoint = '/api/pavement/detect-cracks';\n          break;\n        case 'kerbs':\n          endpoint = '/api/pavement/detect-kerbs';\n          break;\n        default:\n          endpoint = '/api/pavement/detect-all';\n      }\n      const results = [];\n      const filenames = Object.keys(imagePreviewsMap);\n\n      // Process each image sequentially and display immediately\n      for (let i = 0; i < filenames.length; i++) {\n        const filename = filenames[i];\n        const imageData = imagePreviewsMap[filename];\n        try {\n          // Update current image index to show which image is being processed\n          setCurrentImageIndex(i);\n\n          // Get coordinates for this specific image\n          const imageCoordinates = imageLocationMap[filename] || 'Not Available';\n\n          // Prepare request data\n          const requestData = {\n            image: imageData,\n            coordinates: imageCoordinates,\n            username: (user === null || user === void 0 ? void 0 : user.username) || 'Unknown',\n            role: (user === null || user === void 0 ? void 0 : user.role) || 'Unknown'\n          };\n\n          // Make API request\n          const response = await axios.post(endpoint, requestData);\n          if (response.data.success) {\n            // Immediately display the processed image\n            setProcessedImage(response.data.processed_image);\n            setResults(response.data);\n            results.push({\n              filename,\n              success: true,\n              processedImage: response.data.processed_image,\n              data: response.data\n            });\n          } else {\n            const errorMessage = response.data.message || 'Detection failed';\n            results.push({\n              filename,\n              success: false,\n              error: errorMessage,\n              isClassificationError: errorMessage.includes('No road detected')\n            });\n          }\n        } catch (error) {\n          var _error$response2, _error$response2$data;\n          const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'An error occurred during detection';\n          results.push({\n            filename,\n            success: false,\n            error: errorMessage,\n            isClassificationError: errorMessage.includes('No road detected')\n          });\n        }\n\n        // Update progress\n        setProcessedCount(prev => prev + 1);\n\n        // Pause briefly to allow user to see the result before moving to next image\n        // Only pause if not on the last image\n        if (i < filenames.length - 1) {\n          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second pause\n        }\n      }\n\n      // Store final results but don't show the batch summary\n      setBatchResults(results);\n    } catch (error) {\n      setError('Failed to process batch: ' + (error.message || 'Unknown error'));\n    } finally {\n      setBatchProcessing(false);\n    }\n  };\n\n  // Reset detection\n  const handleReset = () => {\n    setImageFiles([]);\n    setImagePreviewsMap({});\n    setImageLocationMap({});\n    setCurrentImageIndex(0);\n    setProcessedImage(null);\n    setResults(null);\n    setError('');\n    setBatchResults([]);\n    setProcessedCount(0);\n    setTotalToProcess(0);\n\n    // Reset coordinates when clearing all images\n    setCoordinates('Not Available');\n    setLocationError('');\n    setLocationPermission('unknown');\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Add a function to handle auto-navigation through results\n  const startAutoNavigation = () => {\n    if (batchResults.length === 0) return;\n\n    // Find only successful results\n    const successfulResults = batchResults.filter(result => result.success);\n    if (successfulResults.length === 0) return;\n    setAutoNavigationActive(true);\n    setAutoNavigationIndex(0);\n\n    // Display the first result\n    const firstResult = successfulResults[0];\n    const fileIndex = Object.keys(imagePreviewsMap).findIndex(filename => filename === firstResult.filename);\n    if (fileIndex !== -1) {\n      setCurrentImageIndex(fileIndex);\n      setProcessedImage(firstResult.processedImage);\n      setResults(firstResult.data);\n    }\n\n    // Set up interval for auto-navigation\n    autoNavigationRef.current = setInterval(() => {\n      setAutoNavigationIndex(prevIndex => {\n        const nextIndex = prevIndex + 1;\n\n        // If we've reached the end, stop auto-navigation\n        if (nextIndex >= successfulResults.length) {\n          clearInterval(autoNavigationRef.current);\n          setAutoNavigationActive(false);\n          return prevIndex;\n        }\n\n        // Display the next result\n        const nextResult = successfulResults[nextIndex];\n        const nextFileIndex = Object.keys(imagePreviewsMap).findIndex(filename => filename === nextResult.filename);\n        if (nextFileIndex !== -1) {\n          setCurrentImageIndex(nextFileIndex);\n          setProcessedImage(nextResult.processedImage);\n          setResults(nextResult.data);\n        }\n        return nextIndex;\n      });\n    }, 3000); // Change results every 3 seconds\n  };\n\n  // Clean up interval on component unmount\n  useEffect(() => {\n    return () => {\n      if (autoNavigationRef.current) {\n        clearInterval(autoNavigationRef.current);\n      }\n    };\n  }, []);\n\n  // Handle location permission changes\n  useEffect(() => {\n    if (cameraActive && locationPermission === 'unknown') {\n      // Try to get location when camera is first activated\n      handleLocationRequest();\n    }\n  }, [cameraActive]);\n\n  // Listen for permission changes if supported\n  useEffect(() => {\n    let permissionWatcher = null;\n    const watchPermissions = async () => {\n      try {\n        if (navigator.permissions && navigator.permissions.query) {\n          const permission = await navigator.permissions.query({\n            name: 'geolocation'\n          });\n          permissionWatcher = () => {\n            setLocationPermission(permission.state);\n            if (permission.state === 'granted' && cameraActive && coordinates === 'Not Available') {\n              handleLocationRequest();\n            }\n          };\n          permission.addEventListener('change', permissionWatcher);\n        }\n      } catch (err) {\n        console.warn('Permission watching not supported:', err);\n      }\n    };\n    watchPermissions();\n    return () => {\n      if (permissionWatcher) {\n        try {\n          const permission = navigator.permissions.query({\n            name: 'geolocation'\n          });\n          permission.then(p => p.removeEventListener('change', permissionWatcher));\n        } catch (err) {\n          console.warn('Error removing permission listener:', err);\n        }\n      }\n    };\n  }, [cameraActive, coordinates]);\n\n  // Force re-render when current image changes to update location display\n  useEffect(() => {\n    // This effect ensures the UI updates when switching between images\n    // The getCurrentImageLocation function will return the correct location for the selected image\n  }, [currentImageIndex, imageLocationMap]);\n\n  // Stop auto-navigation\n  const stopAutoNavigation = () => {\n    if (autoNavigationRef.current) {\n      clearInterval(autoNavigationRef.current);\n      setAutoNavigationActive(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"pavement-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"big-font mb-4\",\n      children: \"Pavement Analysis\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeTab,\n      onSelect: k => setActiveTab(k),\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"detection\",\n        title: \"Image Detection\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Detection Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: detectionType,\n                onChange: e => setDetectionType(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All (Potholes + Cracks + Kerbs)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"potholes\",\n                  children: \"Potholes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"cracks\",\n                  children: \"Alligator Cracks\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"kerbs\",\n                  children: \"Kerbs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(OverlayTrigger, {\n              trigger: \"click\",\n              placement: \"right\",\n              overlay: reminderPopover,\n              rootClose: true,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sticky-note-icon mb-3\",\n                style: {\n                  cursor: 'pointer',\n                  display: 'inline-block'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/remindericon.svg\",\n                  alt: \"Image Upload Guidelines\",\n                  style: {\n                    width: '32px',\n                    height: '32px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Image Source\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: cameraActive ? \"primary\" : \"outline-primary\",\n                  onClick: toggleCamera,\n                  disabled: locationLoading,\n                  children: locationLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      as: \"span\",\n                      animation: \"border\",\n                      size: \"sm\",\n                      role: \"status\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ms-2\",\n                      children: \"Getting Location...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true) : cameraActive ? \"Disable Camera\" : \"Enable Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-input-container\",\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"file-input-label\",\n                    children: [\"Upload Image\", /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      className: \"file-input\",\n                      accept: \"image/*\",\n                      onChange: handleFileChange,\n                      ref: fileInputRef,\n                      disabled: cameraActive,\n                      multiple: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 17\n              }, this), cameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"location-status mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Location Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 693,\n                    columnNumber: 23\n                  }, this), locationPermission === 'granted' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-success ms-1\",\n                    children: \"\\u2713 Enabled\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 60\n                  }, this), locationPermission === 'denied' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-danger ms-1\",\n                    children: \"\\u2717 Denied\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 59\n                  }, this), locationPermission === 'prompt' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-warning ms-1\",\n                    children: \"\\u26A0 Requesting...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 59\n                  }, this), locationPermission === 'unknown' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-secondary ms-1\",\n                    children: \"? Unknown\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 60\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 21\n                }, this), (coordinates !== 'Not Available' || Object.keys(imagePreviewsMap).length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Current Location:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 702,\n                      columnNumber: 27\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-primary\",\n                      children: coordinates\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 702,\n                      columnNumber: 62\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 25\n                  }, this), Object.keys(imagePreviewsMap).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Selected Image Location:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 707,\n                        columnNumber: 31\n                      }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-primary\",\n                        children: getCurrentImageLocation()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 707,\n                        columnNumber: 73\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 706,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 705,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 23\n                }, this), locationError && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"warning\",\n                  className: \"mt-2 mb-0\",\n                  style: {\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n                    as: \"h6\",\n                    children: \"Location Access Issue\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      whiteSpace: 'pre-line'\n                    },\n                    children: locationError\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-end\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-warning\",\n                      size: \"sm\",\n                      onClick: handleLocationRequest,\n                      children: \"Retry Location Access\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 15\n            }, this), cameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"webcam-container mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Webcam, {\n                audio: false,\n                ref: webcamRef,\n                screenshotFormat: \"image/jpeg\",\n                className: \"webcam\",\n                videoConstraints: {\n                  width: 640,\n                  height: 480,\n                  facingMode: cameraOrientation\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 19\n              }, this), isMobile && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-secondary\",\n                onClick: toggleCameraOrientation,\n                className: \"mt-2 mb-2\",\n                size: \"sm\",\n                children: \"Rotate Camera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                onClick: handleCapture,\n                className: \"mt-2\",\n                children: \"Capture Photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this), Object.keys(imagePreviewsMap).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-preview-container mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Previews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"image-gallery\",\n                children: Object.entries(imagePreviewsMap).map(([name, preview], index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `image-thumbnail ${index === currentImageIndex ? 'selected' : ''}`,\n                  onClick: () => setCurrentImageIndex(index),\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: preview,\n                    alt: `Preview ${index + 1}`,\n                    className: \"img-thumbnail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-sm btn-danger remove-image\",\n                    onClick: e => {\n                      e.stopPropagation();\n                      const newFiles = imageFiles.filter((_, i) => i !== index);\n                      const newPreviewsMap = {\n                        ...imagePreviewsMap\n                      };\n                      const newLocationMap = {\n                        ...imageLocationMap\n                      };\n                      delete newPreviewsMap[name];\n                      delete newLocationMap[name];\n                      setImageFiles(newFiles);\n                      setImagePreviewsMap(newPreviewsMap);\n                      setImageLocationMap(newLocationMap);\n                      if (currentImageIndex >= newFiles.length) {\n                        setCurrentImageIndex(Math.max(0, newFiles.length - 1));\n                      }\n                    },\n                    children: \"\\xD7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 25\n                  }, this)]\n                }, name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"current-image-preview\",\n                children: Object.values(imagePreviewsMap)[currentImageIndex] && /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Object.values(imagePreviewsMap)[currentImageIndex],\n                  alt: \"Current Preview\",\n                  className: \"image-preview img-fluid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 17\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 25\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex gap-2 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                onClick: handleProcess,\n                disabled: Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing,\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 821,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ms-2\",\n                    children: \"Detecting...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : `Detect Current Image`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                onClick: handleProcessAll,\n                disabled: Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing,\n                children: batchProcessing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 836,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ms-2\",\n                    children: [\"Processing \", processedCount, \"/\", totalToProcess]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : `Process All Images`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                onClick: handleReset,\n                disabled: loading || batchProcessing,\n                children: \"Reset\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this), processedImage && /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"mb-3\",\n              children: \"Detection Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: (() => {\n                const imageLocation = getCurrentImageLocation();\n                return imageLocation !== 'Not Available' && imageLocation !== 'Location Error' && imageLocation !== 'Permission Denied' ? /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"success\",\n                  className: \"py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-map-marker-alt me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Image Location Data:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 27\n                    }, this), \" \", imageLocation]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"warning\",\n                  className: \"py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-exclamation-triangle me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Location Warning:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 875,\n                      columnNumber: 27\n                    }, this), \" Location data was not available for this image.\", Object.keys(imagePreviewsMap).length > 0 && Object.keys(imagePreviewsMap)[currentImageIndex].includes('camera_capture') ? 'The camera may have been disabled before capturing, or location access was denied.' : 'Uploaded images do not contain GPS data. Use the live camera for location-tagged captures.']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 873,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 23\n                }, this);\n              })()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"processed-image-container mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: processedImage,\n                alt: \"Processed\",\n                className: \"processed-image img-fluid\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 17\n            }, this), results && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"results-summary\",\n              children: [detectionType === 'all' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detection-summary-card\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"mb-4 text-center\",\n                  children: \"\\uD83D\\uDD0D All Defects Detection Results\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 897,\n                  columnNumber: 25\n                }, this), results.model_errors && Object.keys(results.model_errors).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"warning\",\n                  className: \"mb-3 model-error-alert\",\n                  children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n                    as: \"h6\",\n                    children: \"\\u26A0\\uFE0F Partial Detection Results\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Some detection models encountered errors:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 903,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"mb-0\",\n                    children: Object.entries(results.model_errors).map(([model, error]) => /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [model, \":\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 906,\n                        columnNumber: 49\n                      }, this), \" \", error]\n                    }, model, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 906,\n                      columnNumber: 33\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section potholes\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-danger\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83D\\uDD73\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 29\n                    }, this), \"Potholes Detected: \", results.potholes ? results.potholes.length : 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 27\n                  }, this), results.potholes && results.potholes.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"scrollable-table mb-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"table\", {\n                      className: \"table table-striped table-bordered\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 923,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area (cm\\xB2)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 924,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Width (cm)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 925,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Length (cm)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 926,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Max Depth (cm)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 927,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Volume (cm\\xB3)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 928,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Volume Range\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 929,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 922,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 921,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: results.potholes.map(pothole => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: pothole.pothole_id\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 935,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: pothole.area_cm2.toFixed(2)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 936,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: pothole.width_cm ? pothole.width_cm.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 937,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: pothole.length_cm ? pothole.length_cm.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 938,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: pothole.max_depth_cm ? pothole.max_depth_cm.toFixed(2) : pothole.depth_cm.toFixed(2)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 939,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: pothole.volume.toFixed(2)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 940,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: pothole.volume_range\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 941,\n                            columnNumber: 39\n                          }, this)]\n                        }, pothole.pothole_id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 934,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 932,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 920,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No potholes detected in this image.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section cracks\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-success\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83E\\uDEA8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 955,\n                      columnNumber: 29\n                    }, this), \"Alligator Cracks Detected: \", results.cracks ? results.cracks.length : 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 954,\n                    columnNumber: 27\n                  }, this), results.cracks && results.cracks.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"scrollable-table mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table table-striped table-bordered\",\n                        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"ID\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 964,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Type\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 965,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Area (cm\\xB2)\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 966,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Area Range\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 967,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 963,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 962,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: results.cracks.map(crack => /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: crack.crack_id\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 973,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: crack.crack_type\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 974,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: crack.area_cm2.toFixed(2)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 975,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: crack.area_range\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 976,\n                              columnNumber: 41\n                            }, this)]\n                          }, crack.crack_id, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 972,\n                            columnNumber: 39\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 970,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 961,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 960,\n                      columnNumber: 31\n                    }, this), results.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        children: \"Crack Types Summary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 985,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"crack-types-list\",\n                        children: Object.entries(results.type_counts).map(([type, count]) => count > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [type, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 990,\n                            columnNumber: 43\n                          }, this), \" \", count]\n                        }, type, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 989,\n                          columnNumber: 41\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 986,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 984,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No cracks detected in this image.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section kerbs\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-primary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83D\\uDEA7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1006,\n                      columnNumber: 29\n                    }, this), \"Kerbs Detected: \", results.kerbs ? results.kerbs.length : 0]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1005,\n                    columnNumber: 27\n                  }, this), results.kerbs && results.kerbs.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"scrollable-table mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"table\", {\n                        className: \"table table-striped table-bordered\",\n                        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"ID\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1015,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Type\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1016,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Condition\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1017,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                              children: \"Length\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1018,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1014,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1013,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                          children: results.kerbs.map(kerb => /*#__PURE__*/_jsxDEV(\"tr\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              children: kerb.kerb_id\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1024,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: kerb.kerb_type\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1025,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: kerb.condition\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1026,\n                              columnNumber: 41\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              children: kerb.length_m.toFixed(2)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1027,\n                              columnNumber: 41\n                            }, this)]\n                          }, kerb.kerb_id, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1023,\n                            columnNumber: 39\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1021,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1012,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1011,\n                      columnNumber: 31\n                    }, this), results.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                        children: \"Kerb Conditions Summary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1036,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                        className: \"kerb-types-list\",\n                        children: Object.entries(results.condition_counts).map(([condition, count]) => count > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: [condition, \":\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1041,\n                            columnNumber: 43\n                          }, this), \" \", count]\n                        }, condition, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1040,\n                          columnNumber: 41\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1037,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1035,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No kerbs detected in this image.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1050,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"summary-stats\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-3\",\n                    children: \"\\uD83D\\uDCCA Detection Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1056,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"col-md-4 stat-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-value text-danger\",\n                        children: results.potholes ? results.potholes.length : 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1059,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-label\",\n                        children: \"Potholes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1060,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1058,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"col-md-4 stat-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-value text-success\",\n                        children: results.cracks ? results.cracks.length : 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1063,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-label\",\n                        children: \"Cracks\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1064,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1062,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"col-md-4 stat-item\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-value text-primary\",\n                        children: results.kerbs ? results.kerbs.length : 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1067,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"stat-label\",\n                        children: \"Kerbs\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1068,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1066,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1057,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center mt-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"total-defects-badge\",\n                      children: [\"Total Defects: \", (results.potholes ? results.potholes.length : 0) + (results.cracks ? results.cracks.length : 0) + (results.kerbs ? results.kerbs.length : 0)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1072,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 896,\n                columnNumber: 23\n              }, this), detectionType === 'potholes' && results.potholes && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  children: [\"Detected Potholes: \", results.potholes.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 25\n                }, this), results.potholes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"scrollable-table mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"table table-striped table-bordered\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"ID\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1088,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Area (cm\\xB2)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1089,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Width (cm)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1090,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Length (cm)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1091,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Max Depth (cm)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1092,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Volume (cm\\xB3)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1093,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Volume Range\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1094,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1087,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1086,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      children: results.potholes.map(pothole => /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: pothole.pothole_id\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1100,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: pothole.area_cm2.toFixed(2)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1101,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: pothole.width_cm ? pothole.width_cm.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1102,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: pothole.length_cm ? pothole.length_cm.toFixed(2) : 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1103,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: pothole.max_depth_cm ? pothole.max_depth_cm.toFixed(2) : pothole.depth_cm.toFixed(2)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1104,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: pothole.volume.toFixed(2)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1105,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: pothole.volume_range\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1106,\n                          columnNumber: 37\n                        }, this)]\n                      }, pothole.pothole_id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1099,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1097,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1085,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1084,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 23\n              }, this), detectionType === 'cracks' && results.cracks && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  children: [\"Detected Cracks: \", results.cracks.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1118,\n                  columnNumber: 25\n                }, this), results.cracks.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"scrollable-table mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"table table-striped table-bordered\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"ID\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1124,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1125,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Area (cm\\xB2)\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1126,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Area Range\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1127,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1123,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1122,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      children: results.cracks.map(crack => /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: crack.crack_id\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1133,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: crack.crack_type\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1134,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: crack.area_cm2.toFixed(2)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1135,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: crack.area_range\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1136,\n                          columnNumber: 37\n                        }, this)]\n                      }, crack.crack_id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1132,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1130,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1121,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1120,\n                  columnNumber: 27\n                }, this), results.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    children: \"Crack Types Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1146,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"crack-types-list\",\n                    children: Object.entries(results.type_counts).map(([type, count]) => count > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [type, \":\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1151,\n                        columnNumber: 37\n                      }, this), \" \", count]\n                    }, type, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1150,\n                      columnNumber: 35\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1147,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1145,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1117,\n                columnNumber: 23\n              }, this), detectionType === 'kerbs' && results.kerbs && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  children: [\"Detected Kerbs: \", results.kerbs.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1163,\n                  columnNumber: 25\n                }, this), results.kerbs.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"scrollable-table mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"table table-striped table-bordered\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"ID\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1169,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1170,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Condition\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1171,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          children: \"Length\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1172,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1168,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1167,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      children: results.kerbs.map(kerb => /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          children: kerb.kerb_id\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1178,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: kerb.kerb_type\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1179,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: kerb.condition\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1180,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          children: kerb.length_m.toFixed(2)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1181,\n                          columnNumber: 37\n                        }, this)]\n                      }, kerb.kerb_id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1177,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1175,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1166,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 27\n                }, this), results.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    children: \"Kerb Conditions Summary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1191,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    className: \"kerb-conditions-list\",\n                    children: Object.entries(results.condition_counts).map(([condition, count]) => count > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [condition, \":\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1196,\n                        columnNumber: 37\n                      }, this), \" \", count]\n                    }, condition, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1195,\n                      columnNumber: 35\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1192,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1162,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 13\n        }, this), batchProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"batch-processing-status mt-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"me-3\",\n              children: /*#__PURE__*/_jsxDEV(Spinner, {\n                animation: \"border\",\n                size: \"sm\",\n                role: \"status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1216,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1215,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-1\",\n                children: [\"Processing images: \", processedCount, \"/\", totalToProcess]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"progress\",\n                style: {\n                  height: '10px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress-bar\",\n                  role: \"progressbar\",\n                  style: {\n                    width: `${processedCount / totalToProcess * 100}%`\n                  },\n                  \"aria-valuenow\": processedCount,\n                  \"aria-valuemin\": \"0\",\n                  \"aria-valuemax\": totalToProcess\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1221,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1220,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1214,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1213,\n          columnNumber: 13\n        }, this), !batchProcessing && batchResults.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"batch-complete-status mt-3\",\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"success\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check-circle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1238,\n              columnNumber: 17\n            }, this), \"Processed \", batchResults.length, \" images.\", batchResults.filter(r => r.success).length, \" successful,\", batchResults.filter(r => !r.success && !r.isClassificationError).length, \" failed.\", batchResults.filter(r => r.isClassificationError).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-warning\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-exclamation-triangle me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1245,\n                  columnNumber: 23\n                }, this), batchResults.filter(r => r.isClassificationError).length, \" image(s) contained no road.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1244,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1237,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1236,\n          columnNumber: 13\n        }, this), batchResults.length > 1 && !batchProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image-navigation mt-3 d-flex justify-content-between\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-secondary\",\n            size: \"sm\",\n            disabled: currentImageIndex === 0,\n            onClick: () => {\n              if (currentImageIndex > 0) {\n                const newIndex = currentImageIndex - 1;\n                setCurrentImageIndex(newIndex);\n\n                // Find the result for this image\n                const filename = Object.keys(imagePreviewsMap)[newIndex];\n                const result = batchResults.find(r => r.filename === filename);\n                if (result && result.success) {\n                  setProcessedImage(result.processedImage);\n                  setResults(result.data);\n                } else {\n                  setProcessedImage(null);\n                  setResults(null);\n                }\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-arrow-left me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1280,\n              columnNumber: 17\n            }, this), \" Previous Image\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-counter\",\n            children: [\"Image \", currentImageIndex + 1, \" of \", Object.keys(imagePreviewsMap).length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1283,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-secondary\",\n            size: \"sm\",\n            disabled: currentImageIndex >= Object.keys(imagePreviewsMap).length - 1,\n            onClick: () => {\n              if (currentImageIndex < Object.keys(imagePreviewsMap).length - 1) {\n                const newIndex = currentImageIndex + 1;\n                setCurrentImageIndex(newIndex);\n\n                // Find the result for this image\n                const filename = Object.keys(imagePreviewsMap)[newIndex];\n                const result = batchResults.find(r => r.filename === filename);\n                if (result && result.success) {\n                  setProcessedImage(result.processedImage);\n                  setResults(result.data);\n                } else {\n                  setProcessedImage(null);\n                  setResults(null);\n                }\n              }\n            },\n            children: [\"Next Image \", /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-arrow-right ms-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1310,\n              columnNumber: 28\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1287,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1256,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"video\",\n        title: \"Video Detection\",\n        children: /*#__PURE__*/_jsxDEV(VideoDefectDetection, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1317,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        eventKey: \"information\",\n        title: \"Information\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"About Pavement Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The Pavement Analysis module uses advanced computer vision to detect and analyze various types of pavement defects and features:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"1. Potholes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Potholes are bowl-shaped holes of various sizes in the road surface that can be a serious hazard to vehicles. The system detects potholes and calculates:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Area in square centimeters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Depth in centimeters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Volume\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1337,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Classification by size (Small, Medium, Large)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1338,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"2. Alligator Cracks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Alligator cracks are a series of interconnected cracks creating a pattern resembling an alligator's scales. These indicate underlying structural weakness. The system identifies multiple types of cracks including:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Alligator Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Edge Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Hairline Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Longitudinal Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Transverse Cracks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1352,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"3. Kerbs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Kerbs are raised edges along a street or path that define boundaries between roadways and other areas. The system identifies different kerb conditions including:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Normal/Good Kerbs - Structurally sound and properly visible\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1361,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Faded Kerbs - Reduced visibility due to worn paint or weathering\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1362,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Damaged Kerbs - Physically damaged or broken kerbs requiring repair\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1363,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1360,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Location Services & GPS Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1366,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"When using the live camera option, the application can capture GPS coordinates to provide precise geolocation data for detected defects. This helps in:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Accurately mapping defect locations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Creating location-based reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1373,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Enabling field teams to find specific issues\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1374,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Tracking defect patterns by geographic area\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1375,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Location Requirements:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Secure Connection:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1380,\n                  columnNumber: 21\n                }, this), \" Location services require HTTPS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Browser Permissions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1381,\n                  columnNumber: 21\n                }, this), \" You must allow location access when prompted\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Safari Users:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1382,\n                  columnNumber: 21\n                }, this), \" Enable location services in Safari settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1382,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Mobile Devices:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1383,\n                  columnNumber: 21\n                }, this), \" Ensure location services are enabled in device settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1383,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-info-circle me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1387,\n                  columnNumber: 21\n                }, this), \"Troubleshooting Location Issues\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1387,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"If location access is denied:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1388,\n                  columnNumber: 20\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1388,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Safari:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1390,\n                    columnNumber: 23\n                  }, this), \" Settings \\u2192 Privacy & Security \\u2192 Location Services\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1390,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Chrome:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1391,\n                    columnNumber: 23\n                  }, this), \" Settings \\u2192 Privacy and security \\u2192 Site Settings \\u2192 Location\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Firefox:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1392,\n                    columnNumber: 23\n                  }, this), \" Settings \\u2192 Privacy & Security \\u2192 Permissions \\u2192 Location\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1392,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1389,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"On mobile devices:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1394,\n                  columnNumber: 20\n                }, this), \" Also check your device's location settings and ensure the browser has location permission.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1394,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1386,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"How to Use This Module\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Select the detection type (Potholes, Alligator Cracks, or Kerbs)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1399,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Upload an image or use the camera to capture a photo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1400,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"If using the camera, allow location access when prompted for GPS coordinates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1401,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Click the Detect button to analyze the image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1402,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Review the detection results and measurements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1403,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The detected defects are automatically recorded in the database for tracking and analysis in the Dashboard module.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1321,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1320,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showClassificationModal,\n      onHide: () => setShowClassificationModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-exclamation-triangle text-warning me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1423,\n            columnNumber: 13\n          }, this), \"Road Detection Failed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1422,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1421,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-road fa-3x text-muted\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1430,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"text-danger mb-3\",\n            children: \"No Road Detected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1432,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-3\",\n            children: classificationError || 'The uploaded image does not appear to contain a road. Please upload an image that clearly shows a road surface for defect detection.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Tips for better results:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"mb-0 mt-2 text-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Ensure the image clearly shows a road surface\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1439,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Avoid images with only buildings, sky, or vegetation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1440,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Make sure the road takes up a significant portion of the image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1441,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Use good lighting conditions for clearer road visibility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1442,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1438,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1436,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1428,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1427,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: () => setShowClassificationModal(false),\n          children: \"Try Another Image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1448,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1447,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1416,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 613,\n    columnNumber: 5\n  }, this);\n};\n_s(Pavement, \"sKenzTb6ldTKmgN/emMbkyCly1c=\", false, function () {\n  return [useResponsive];\n});\n_c = Pavement;\nexport default Pavement;\nvar _c;\n$RefreshReg$(_c, \"Pavement\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Container", "Card", "<PERSON><PERSON>", "Form", "Tabs", "Tab", "<PERSON><PERSON>", "Spinner", "OverlayTrigger", "Popover", "Modal", "axios", "Webcam", "useResponsive", "VideoDefectDetection", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Pavement", "_s", "activeTab", "setActiveTab", "detectionType", "setDetectionType", "imageFiles", "setImageFiles", "imagePreviewsMap", "setImagePreviewsMap", "imageLocationMap", "setImageLocationMap", "currentImageIndex", "setCurrentImageIndex", "processedImage", "setProcessedImage", "results", "setResults", "loading", "setLoading", "error", "setError", "cameraActive", "setCameraActive", "coordinates", "setCoordinates", "cameraOrientation", "setCameraOrientation", "locationPermission", "setLocationPermission", "locationError", "setLocationError", "locationLoading", "setLocationLoading", "batchResults", "setBatchResults", "batchProcessing", "setBatchProcessing", "processedCount", "setProcessedCount", "showClassificationModal", "setShowClassificationModal", "classificationError", "setClassificationError", "totalToProcess", "setTotalToProcess", "autoNavigationActive", "setAutoNavigationActive", "autoNavigationIndex", "setAutoNavigationIndex", "autoNavigationRef", "webcamRef", "fileInputRef", "isMobile", "reminderPopover", "id", "style", "max<PERSON><PERSON><PERSON>", "children", "Header", "as", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "marginBottom", "paddingLeft", "checkLocationPermission", "navigator", "permissions", "query", "permission", "name", "state", "err", "console", "warn", "requestLocation", "Promise", "resolve", "reject", "geolocation", "Error", "window", "isSecureContext", "options", "enableHighAccuracy", "timeout", "maximumAge", "getCurrentPosition", "position", "errorMessage", "code", "PERMISSION_DENIED", "POSITION_UNAVAILABLE", "TIMEOUT", "message", "handleLocationRequest", "permissionState", "errorMsg", "latitude", "longitude", "coords", "formattedCoords", "toFixed", "log", "accuracy", "includes", "handleFileChange", "e", "files", "Array", "from", "target", "length", "for<PERSON>ach", "file", "reader", "FileReader", "onloadend", "prev", "result", "readAsDataURL", "handleCapture", "imageSrc", "current", "getScreenshot", "timestamp", "Date", "toISOString", "filename", "captureCoordinates", "getCurrentImageLocation", "Object", "keys", "currentFilename", "toggleCamera", "newCameraState", "toggleCameraOrientation", "handleClassificationError", "handleProcess", "userString", "sessionStorage", "getItem", "user", "JSON", "parse", "currentImagePreview", "values", "imageCoordinates", "requestData", "image", "username", "role", "endpoint", "response", "post", "data", "success", "processed_image", "_error$response", "_error$response$data", "handleProcessAll", "filenames", "i", "imageData", "push", "isClassificationError", "_error$response2", "_error$response2$data", "setTimeout", "handleReset", "value", "startAutoNavigation", "successfulResults", "filter", "firstResult", "fileIndex", "findIndex", "setInterval", "prevIndex", "nextIndex", "clearInterval", "nextResult", "nextFileIndex", "permissionWatcher", "watchPermissions", "addEventListener", "then", "p", "removeEventListener", "stopAutoNavigation", "className", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title", "Group", "Label", "Select", "onChange", "trigger", "placement", "overlay", "rootClose", "cursor", "display", "src", "alt", "width", "height", "variant", "onClick", "disabled", "animation", "size", "type", "accept", "ref", "multiple", "fontSize", "Heading", "whiteSpace", "audio", "screenshotFormat", "videoConstraints", "facingMode", "entries", "map", "preview", "index", "stopPropagation", "newFiles", "_", "newPreviewsMap", "newLocationMap", "Math", "max", "imageLocation", "model_errors", "model", "potholes", "pothole", "pothole_id", "area_cm2", "width_cm", "length_cm", "max_depth_cm", "depth_cm", "volume", "volume_range", "cracks", "crack", "crack_id", "crack_type", "area_range", "type_counts", "count", "kerbs", "kerb", "kerb_id", "kerb_type", "condition", "length_m", "condition_counts", "r", "newIndex", "find", "show", "onHide", "centered", "closeButton", "Title", "Footer", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTA_GIT/LTA/frontend/src/pages/Pavement.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ton, Form, Tabs, Tab, <PERSON><PERSON>, Spinner, OverlayTrigger, Popover, Modal } from 'react-bootstrap';\r\nimport axios from 'axios';\r\nimport Webcam from 'react-webcam';\r\nimport './Pavement.css';\r\nimport useResponsive from '../hooks/useResponsive';\r\nimport VideoDefectDetection from '../components/VideoDefectDetection';\r\n\r\nconst Pavement = () => {\r\n  const [activeTab, setActiveTab] = useState('detection');\r\n  const [detectionType, setDetectionType] = useState('all');\r\n  const [imageFiles, setImageFiles] = useState([]);\r\n  const [imagePreviewsMap, setImagePreviewsMap] = useState({});\r\n  const [imageLocationMap, setImageLocationMap] = useState({});\r\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\r\n  const [processedImage, setProcessedImage] = useState(null);\r\n  const [results, setResults] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [cameraActive, setCameraActive] = useState(false);\r\n  const [coordinates, setCoordinates] = useState('Not Available');\r\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\r\n  const [locationPermission, setLocationPermission] = useState('unknown');\r\n  const [locationError, setLocationError] = useState('');\r\n  const [locationLoading, setLocationLoading] = useState(false);\r\n  \r\n  // Add state for batch processing results\r\n  const [batchResults, setBatchResults] = useState([]);\r\n  const [batchProcessing, setBatchProcessing] = useState(false);\r\n  const [processedCount, setProcessedCount] = useState(0);\r\n\r\n  // Add state for classification error modal\r\n  const [showClassificationModal, setShowClassificationModal] = useState(false);\r\n  const [classificationError, setClassificationError] = useState('');\r\n  const [totalToProcess, setTotalToProcess] = useState(0);\r\n  \r\n  // Add state for auto-navigation through results\r\n  const [autoNavigationActive, setAutoNavigationActive] = useState(false);\r\n  const [autoNavigationIndex, setAutoNavigationIndex] = useState(0);\r\n  const autoNavigationRef = useRef(null);\r\n  \r\n  const webcamRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const { isMobile } = useResponsive();\r\n\r\n  // Create the popover content\r\n  const reminderPopover = (\r\n    <Popover id=\"reminder-popover\" style={{ maxWidth: '300px' }}>\r\n      <Popover.Header as=\"h3\">📸 Image Upload Guidelines</Popover.Header>\r\n      <Popover.Body>\r\n        <p style={{ marginBottom: '10px' }}>\r\n          Please ensure your uploaded images are:\r\n        </p>\r\n        <ul style={{ marginBottom: '0', paddingLeft: '20px' }}>\r\n          <li>Focused directly on the road surface</li>\r\n          <li>Well-lit and clear</li>\r\n          <li>Showing the entire area of concern</li>\r\n          <li>Taken from a reasonable distance to capture context</li>\r\n        </ul>\r\n      </Popover.Body>\r\n    </Popover>\r\n  );\r\n\r\n  // Safari-compatible geolocation permission check\r\n  const checkLocationPermission = async () => {\r\n    if (!navigator.permissions || !navigator.permissions.query) {\r\n      // Fallback for older browsers\r\n      return 'prompt';\r\n    }\r\n    \r\n    try {\r\n      const permission = await navigator.permissions.query({ name: 'geolocation' });\r\n      return permission.state;\r\n    } catch (err) {\r\n      console.warn('Permission API not supported or failed:', err);\r\n      return 'prompt';\r\n    }\r\n  };\r\n\r\n  // Safari-compatible geolocation request\r\n  const requestLocation = () => {\r\n    return new Promise((resolve, reject) => {\r\n      // Check if geolocation is supported\r\n      if (!navigator.geolocation) {\r\n        reject(new Error('Geolocation is not supported by this browser'));\r\n        return;\r\n      }\r\n\r\n      // Check if we're in a secure context (HTTPS)\r\n      if (!window.isSecureContext) {\r\n        reject(new Error('Geolocation requires a secure context (HTTPS)'));\r\n        return;\r\n      }\r\n\r\n      const options = {\r\n        enableHighAccuracy: true,\r\n        timeout: 15000, // 15 seconds timeout\r\n        maximumAge: 60000 // Accept cached position up to 1 minute old\r\n      };\r\n\r\n      navigator.geolocation.getCurrentPosition(\r\n        (position) => {\r\n          resolve(position);\r\n        },\r\n        (error) => {\r\n          let errorMessage = 'Unable to retrieve location';\r\n          \r\n          switch (error.code) {\r\n            case error.PERMISSION_DENIED:\r\n              errorMessage = 'Location access denied. Please enable location permissions in your browser settings.';\r\n              break;\r\n            case error.POSITION_UNAVAILABLE:\r\n              errorMessage = 'Location information is unavailable. Please try again.';\r\n              break;\r\n            case error.TIMEOUT:\r\n              errorMessage = 'Location request timed out. Please try again.';\r\n              break;\r\n            default:\r\n              errorMessage = `Location error: ${error.message}`;\r\n              break;\r\n          }\r\n          \r\n          reject(new Error(errorMessage));\r\n        },\r\n        options\r\n      );\r\n    });\r\n  };\r\n\r\n  // Enhanced location handler with Safari-specific fixes\r\n  const handleLocationRequest = async () => {\r\n    setLocationLoading(true);\r\n    setLocationError('');\r\n    \r\n    try {\r\n      // First check permission state\r\n      const permissionState = await checkLocationPermission();\r\n      setLocationPermission(permissionState);\r\n      \r\n      // If permission is denied, provide user guidance\r\n      if (permissionState === 'denied') {\r\n        const errorMsg = 'Location access denied. To enable location access:\\n' +\r\n                        '• Safari: Settings > Privacy & Security > Location Services\\n' +\r\n                        '• Chrome: Settings > Privacy > Location\\n' +\r\n                        '• Firefox: Settings > Privacy > Location\\n' +\r\n                        'Then refresh this page and try again.';\r\n        setLocationError(errorMsg);\r\n        setCoordinates('Permission Denied');\r\n        return;\r\n      }\r\n      \r\n      // Request location\r\n      const position = await requestLocation();\r\n      const { latitude, longitude } = position.coords;\r\n      \r\n      // Format coordinates with better precision\r\n      const formattedCoords = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;\r\n      setCoordinates(formattedCoords);\r\n      setLocationPermission('granted');\r\n      setLocationError('');\r\n      \r\n      console.log('Location acquired:', { latitude, longitude, accuracy: position.coords.accuracy });\r\n      \r\n    } catch (error) {\r\n      console.error('Location request failed:', error);\r\n      setLocationError(error.message);\r\n      setCoordinates('Location Error');\r\n      \r\n      // Update permission state based on error\r\n      if (error.message.includes('denied')) {\r\n        setLocationPermission('denied');\r\n      }\r\n    } finally {\r\n      setLocationLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle multiple file input change\r\n  const handleFileChange = (e) => {\r\n    const files = Array.from(e.target.files);\r\n    if (files.length > 0) {\r\n      setImageFiles([...imageFiles, ...files]);\r\n      \r\n      // Create previews and location data for each file\r\n      files.forEach(file => {\r\n        const reader = new FileReader();\r\n        reader.onloadend = () => {\r\n          setImagePreviewsMap(prev => ({\r\n            ...prev,\r\n            [file.name]: reader.result\r\n          }));\r\n        };\r\n        reader.readAsDataURL(file);\r\n        \r\n        // Store location as \"Not Available\" for uploaded files\r\n        setImageLocationMap(prev => ({\r\n          ...prev,\r\n          [file.name]: 'Not Available'\r\n        }));\r\n      });\r\n      \r\n      // Reset results\r\n      setProcessedImage(null);\r\n      setResults(null);\r\n      setError('');\r\n    }\r\n  };\r\n\r\n  // Handle camera capture with location validation\r\n  const handleCapture = async () => {\r\n    const imageSrc = webcamRef.current.getScreenshot();\r\n    if (imageSrc) {\r\n      // If we don't have location data, try to get it before capturing\r\n      if (coordinates === 'Not Available' || coordinates === 'Location Error') {\r\n        await handleLocationRequest();\r\n      }\r\n      \r\n      const timestamp = new Date().toISOString();\r\n      const filename = `camera_capture_${timestamp}.jpg`;\r\n      const captureCoordinates = coordinates; // Capture current coordinates\r\n      \r\n      setImageFiles([...imageFiles, filename]);\r\n      setImagePreviewsMap(prev => ({\r\n        ...prev,\r\n        [filename]: imageSrc\r\n      }));\r\n      setImageLocationMap(prev => ({\r\n        ...prev,\r\n        [filename]: captureCoordinates\r\n      }));\r\n      setCurrentImageIndex(imageFiles.length);\r\n      \r\n      setProcessedImage(null);\r\n      setResults(null);\r\n      setError('');\r\n      \r\n      // Log capture with current coordinates\r\n      console.log('Photo captured with coordinates:', captureCoordinates);\r\n    }\r\n  };\r\n\r\n  // Get location data for currently selected image\r\n  const getCurrentImageLocation = () => {\r\n    if (Object.keys(imagePreviewsMap).length === 0) {\r\n      return coordinates; // Use current coordinates if no images\r\n    }\r\n    \r\n    const currentFilename = Object.keys(imagePreviewsMap)[currentImageIndex];\r\n    return imageLocationMap[currentFilename] || 'Not Available';\r\n  };\r\n\r\n  // Toggle camera with improved location handling\r\n  const toggleCamera = async () => {\r\n    const newCameraState = !cameraActive;\r\n    setCameraActive(newCameraState);\r\n    \r\n    if (newCameraState) {\r\n      // Get location when camera is activated\r\n      await handleLocationRequest();\r\n    } else {\r\n      // Only reset location if no images are captured\r\n      // This preserves location data for captured images\r\n      if (Object.keys(imagePreviewsMap).length === 0) {\r\n        setCoordinates('Not Available');\r\n        setLocationError('');\r\n        setLocationPermission('unknown');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Toggle camera orientation (front/back) for mobile devices\r\n  const toggleCameraOrientation = () => {\r\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\r\n  };\r\n\r\n  // Helper function to handle classification errors\r\n  const handleClassificationError = (errorMessage) => {\r\n    setClassificationError(errorMessage);\r\n    setShowClassificationModal(true);\r\n    setError(''); // Clear general error since we're showing specific modal\r\n  };\r\n\r\n  // Process image for detection\r\n  const handleProcess = async () => {\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      // Get user info from session storage\r\n      const userString = sessionStorage.getItem('user');\r\n      const user = userString ? JSON.parse(userString) : null;\r\n      \r\n      // Get the currently selected image\r\n      const currentImagePreview = Object.values(imagePreviewsMap)[currentImageIndex];\r\n      \r\n      if (!currentImagePreview) {\r\n        setError('No image selected for processing');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // Get coordinates for the current image\r\n      const imageCoordinates = getCurrentImageLocation();\r\n      \r\n      // Prepare request data\r\n      const requestData = {\r\n        image: currentImagePreview,\r\n        coordinates: imageCoordinates,\r\n        username: user?.username || 'Unknown',\r\n        role: user?.role || 'Unknown'\r\n      };\r\n\r\n      // Determine endpoint based on detection type\r\n      let endpoint;\r\n      switch(detectionType) {\r\n        case 'all':\r\n          endpoint = '/api/pavement/detect-all';\r\n          break;\r\n        case 'potholes':\r\n          endpoint = '/api/pavement/detect-potholes';\r\n          break;\r\n        case 'cracks':\r\n          endpoint = '/api/pavement/detect-cracks';\r\n          break;\r\n        case 'kerbs':\r\n          endpoint = '/api/pavement/detect-kerbs';\r\n          break;\r\n        default:\r\n          endpoint = '/api/pavement/detect-all';\r\n      }\r\n\r\n      // Make API request\r\n      const response = await axios.post(endpoint, requestData);\r\n\r\n      // Handle response\r\n      if (response.data.success) {\r\n        setProcessedImage(response.data.processed_image);\r\n        setResults(response.data);\r\n      } else {\r\n        setError(response.data.message || 'Detection failed');\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error.response?.data?.message || 'An error occurred during detection. Please try again.';\r\n\r\n      // Check if this is a classification error (no road detected)\r\n      if (errorMessage.includes('No road detected')) {\r\n        handleClassificationError(errorMessage);\r\n      } else {\r\n        setError(errorMessage);\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Add a new function to process all images\r\n  const handleProcessAll = async () => {\r\n    if (Object.keys(imagePreviewsMap).length === 0) {\r\n      setError('No images to process');\r\n      return;\r\n    }\r\n\r\n    setBatchProcessing(true);\r\n    setError('');\r\n    setBatchResults([]);\r\n    setProcessedCount(0);\r\n    setTotalToProcess(Object.keys(imagePreviewsMap).length);\r\n    \r\n    // Get user info from session storage\r\n    const userString = sessionStorage.getItem('user');\r\n    const user = userString ? JSON.parse(userString) : null;\r\n    \r\n    try {\r\n      // Determine endpoint based on detection type\r\n      let endpoint;\r\n      switch(detectionType) {\r\n        case 'all':\r\n          endpoint = '/api/pavement/detect-all';\r\n          break;\r\n        case 'potholes':\r\n          endpoint = '/api/pavement/detect-potholes';\r\n          break;\r\n        case 'cracks':\r\n          endpoint = '/api/pavement/detect-cracks';\r\n          break;\r\n        case 'kerbs':\r\n          endpoint = '/api/pavement/detect-kerbs';\r\n          break;\r\n        default:\r\n          endpoint = '/api/pavement/detect-all';\r\n      }\r\n      \r\n      const results = [];\r\n      const filenames = Object.keys(imagePreviewsMap);\r\n      \r\n      // Process each image sequentially and display immediately\r\n      for (let i = 0; i < filenames.length; i++) {\r\n        const filename = filenames[i];\r\n        const imageData = imagePreviewsMap[filename];\r\n        \r\n        try {\r\n          // Update current image index to show which image is being processed\r\n          setCurrentImageIndex(i);\r\n          \r\n          // Get coordinates for this specific image\r\n          const imageCoordinates = imageLocationMap[filename] || 'Not Available';\r\n          \r\n          // Prepare request data\r\n          const requestData = {\r\n            image: imageData,\r\n            coordinates: imageCoordinates,\r\n            username: user?.username || 'Unknown',\r\n            role: user?.role || 'Unknown'\r\n          };\r\n          \r\n          // Make API request\r\n          const response = await axios.post(endpoint, requestData);\r\n          \r\n          if (response.data.success) {\r\n            // Immediately display the processed image\r\n            setProcessedImage(response.data.processed_image);\r\n            setResults(response.data);\r\n            \r\n            results.push({\r\n              filename,\r\n              success: true,\r\n              processedImage: response.data.processed_image,\r\n              data: response.data\r\n            });\r\n          } else {\r\n            const errorMessage = response.data.message || 'Detection failed';\r\n            results.push({\r\n              filename,\r\n              success: false,\r\n              error: errorMessage,\r\n              isClassificationError: errorMessage.includes('No road detected')\r\n            });\r\n          }\r\n        } catch (error) {\r\n          const errorMessage = error.response?.data?.message || 'An error occurred during detection';\r\n          results.push({\r\n            filename,\r\n            success: false,\r\n            error: errorMessage,\r\n            isClassificationError: errorMessage.includes('No road detected')\r\n          });\r\n        }\r\n        \r\n        // Update progress\r\n        setProcessedCount(prev => prev + 1);\r\n        \r\n        // Pause briefly to allow user to see the result before moving to next image\r\n        // Only pause if not on the last image\r\n        if (i < filenames.length - 1) {\r\n          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second pause\r\n        }\r\n      }\r\n      \r\n      // Store final results but don't show the batch summary\r\n      setBatchResults(results);\r\n      \r\n    } catch (error) {\r\n      setError('Failed to process batch: ' + (error.message || 'Unknown error'));\r\n    } finally {\r\n      setBatchProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Reset detection\r\n  const handleReset = () => {\r\n    setImageFiles([]);\r\n    setImagePreviewsMap({});\r\n    setImageLocationMap({});\r\n    setCurrentImageIndex(0);\r\n    setProcessedImage(null);\r\n    setResults(null);\r\n    setError('');\r\n    setBatchResults([]);\r\n    setProcessedCount(0);\r\n    setTotalToProcess(0);\r\n    \r\n    // Reset coordinates when clearing all images\r\n    setCoordinates('Not Available');\r\n    setLocationError('');\r\n    setLocationPermission('unknown');\r\n    \r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Add a function to handle auto-navigation through results\r\n  const startAutoNavigation = () => {\r\n    if (batchResults.length === 0) return;\r\n    \r\n    // Find only successful results\r\n    const successfulResults = batchResults.filter(result => result.success);\r\n    if (successfulResults.length === 0) return;\r\n    \r\n    setAutoNavigationActive(true);\r\n    setAutoNavigationIndex(0);\r\n    \r\n    // Display the first result\r\n    const firstResult = successfulResults[0];\r\n    const fileIndex = Object.keys(imagePreviewsMap).findIndex(\r\n      filename => filename === firstResult.filename\r\n    );\r\n    \r\n    if (fileIndex !== -1) {\r\n      setCurrentImageIndex(fileIndex);\r\n      setProcessedImage(firstResult.processedImage);\r\n      setResults(firstResult.data);\r\n    }\r\n    \r\n    // Set up interval for auto-navigation\r\n    autoNavigationRef.current = setInterval(() => {\r\n      setAutoNavigationIndex(prevIndex => {\r\n        const nextIndex = prevIndex + 1;\r\n        \r\n        // If we've reached the end, stop auto-navigation\r\n        if (nextIndex >= successfulResults.length) {\r\n          clearInterval(autoNavigationRef.current);\r\n          setAutoNavigationActive(false);\r\n          return prevIndex;\r\n        }\r\n        \r\n        // Display the next result\r\n        const nextResult = successfulResults[nextIndex];\r\n        const nextFileIndex = Object.keys(imagePreviewsMap).findIndex(\r\n          filename => filename === nextResult.filename\r\n        );\r\n        \r\n        if (nextFileIndex !== -1) {\r\n          setCurrentImageIndex(nextFileIndex);\r\n          setProcessedImage(nextResult.processedImage);\r\n          setResults(nextResult.data);\r\n        }\r\n        \r\n        return nextIndex;\r\n      });\r\n    }, 3000); // Change results every 3 seconds\r\n  };\r\n\r\n  // Clean up interval on component unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (autoNavigationRef.current) {\r\n        clearInterval(autoNavigationRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Handle location permission changes\r\n  useEffect(() => {\r\n    if (cameraActive && locationPermission === 'unknown') {\r\n      // Try to get location when camera is first activated\r\n      handleLocationRequest();\r\n    }\r\n  }, [cameraActive]);\r\n\r\n  // Listen for permission changes if supported\r\n  useEffect(() => {\r\n    let permissionWatcher = null;\r\n    \r\n    const watchPermissions = async () => {\r\n      try {\r\n        if (navigator.permissions && navigator.permissions.query) {\r\n          const permission = await navigator.permissions.query({ name: 'geolocation' });\r\n          \r\n          permissionWatcher = () => {\r\n            setLocationPermission(permission.state);\r\n            if (permission.state === 'granted' && cameraActive && coordinates === 'Not Available') {\r\n              handleLocationRequest();\r\n            }\r\n          };\r\n          \r\n          permission.addEventListener('change', permissionWatcher);\r\n        }\r\n      } catch (err) {\r\n        console.warn('Permission watching not supported:', err);\r\n      }\r\n    };\r\n    \r\n    watchPermissions();\r\n    \r\n    return () => {\r\n      if (permissionWatcher) {\r\n        try {\r\n          const permission = navigator.permissions.query({ name: 'geolocation' });\r\n          permission.then(p => p.removeEventListener('change', permissionWatcher));\r\n        } catch (err) {\r\n          console.warn('Error removing permission listener:', err);\r\n        }\r\n      }\r\n    };\r\n  }, [cameraActive, coordinates]);\r\n\r\n  // Force re-render when current image changes to update location display\r\n  useEffect(() => {\r\n    // This effect ensures the UI updates when switching between images\r\n    // The getCurrentImageLocation function will return the correct location for the selected image\r\n  }, [currentImageIndex, imageLocationMap]);\r\n\r\n  // Stop auto-navigation\r\n  const stopAutoNavigation = () => {\r\n    if (autoNavigationRef.current) {\r\n      clearInterval(autoNavigationRef.current);\r\n      setAutoNavigationActive(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container className=\"pavement-page\">\r\n      <h2 className=\"big-font mb-4\">Pavement Analysis</h2>\r\n      \r\n      <Tabs\r\n        activeKey={activeTab}\r\n        onSelect={(k) => setActiveTab(k)}\r\n        className=\"mb-4\"\r\n      >\r\n        <Tab eventKey=\"detection\" title=\"Image Detection\">\r\n          <Card className=\"mb-4\">\r\n            <Card.Body>\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Detection Type</Form.Label>\r\n                <Form.Select \r\n                  value={detectionType}\r\n                  onChange={(e) => setDetectionType(e.target.value)}\r\n                >\r\n                  <option value=\"all\">All (Potholes + Cracks + Kerbs)</option>\r\n                  <option value=\"potholes\">Potholes</option>\r\n                  <option value=\"cracks\">Alligator Cracks</option>\r\n                  <option value=\"kerbs\">Kerbs</option>\r\n                </Form.Select>\r\n              </Form.Group>\r\n\r\n                              {/* Sticky note reminder */}\r\n                <OverlayTrigger \r\n                  trigger=\"click\" \r\n                  placement=\"right\" \r\n                  overlay={reminderPopover}\r\n                  rootClose\r\n                >\r\n                  <div \r\n                    className=\"sticky-note-icon mb-3\"\r\n                    style={{ cursor: 'pointer', display: 'inline-block' }}\r\n                  >\r\n                    <img \r\n                      src=\"/remindericon.svg\" \r\n                      alt=\"Image Upload Guidelines\" \r\n                      style={{ width: '32px', height: '32px' }}\r\n                    />\r\n                  </div>\r\n                </OverlayTrigger>\r\n\r\n              <div className=\"mb-3\">\r\n                <Form.Label>Image Source</Form.Label>\r\n                <div className=\"d-flex gap-2 mb-2\">\r\n                  <Button \r\n                    variant={cameraActive ? \"primary\" : \"outline-primary\"}\r\n                    onClick={toggleCamera}\r\n                    disabled={locationLoading}\r\n                  >\r\n                    {locationLoading ? (\r\n                      <>\r\n                        <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\r\n                        <span className=\"ms-2\">Getting Location...</span>\r\n                      </>\r\n                    ) : (\r\n                      cameraActive ? \"Disable Camera\" : \"Enable Camera\"\r\n                    )}\r\n                  </Button>\r\n                  <div className=\"file-input-container\">\r\n                    <label className=\"file-input-label\">\r\n                      Upload Image\r\n                      <input\r\n                        type=\"file\"\r\n                        className=\"file-input\"\r\n                        accept=\"image/*\"\r\n                        onChange={handleFileChange}\r\n                        ref={fileInputRef}\r\n                        disabled={cameraActive}\r\n                        multiple\r\n                      />\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* Location Status Display */}\r\n                {cameraActive && (\r\n                  <div className=\"location-status mb-3\">\r\n                    <small className=\"text-muted\">\r\n                      <strong>Location Status:</strong> \r\n                      {locationPermission === 'granted' && <span className=\"text-success ms-1\">✓ Enabled</span>}\r\n                      {locationPermission === 'denied' && <span className=\"text-danger ms-1\">✗ Denied</span>}\r\n                      {locationPermission === 'prompt' && <span className=\"text-warning ms-1\">⚠ Requesting...</span>}\r\n                      {locationPermission === 'unknown' && <span className=\"text-secondary ms-1\">? Unknown</span>}\r\n                    </small>\r\n                    {(coordinates !== 'Not Available' || Object.keys(imagePreviewsMap).length > 0) && (\r\n                      <div className=\"mt-1\">\r\n                        <small className=\"text-muted\">\r\n                          <strong>Current Location:</strong> <span className=\"text-primary\">{coordinates}</span>\r\n                        </small>\r\n                        {Object.keys(imagePreviewsMap).length > 0 && (\r\n                          <div className=\"mt-1\">\r\n                            <small className=\"text-muted\">\r\n                              <strong>Selected Image Location:</strong> <span className=\"text-primary\">{getCurrentImageLocation()}</span>\r\n                            </small>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                    {locationError && (\r\n                      <Alert variant=\"warning\" className=\"mt-2 mb-0\" style={{ fontSize: '0.875rem' }}>\r\n                        <Alert.Heading as=\"h6\">Location Access Issue</Alert.Heading>\r\n                        <div style={{ whiteSpace: 'pre-line' }}>{locationError}</div>\r\n                        <hr />\r\n                        <div className=\"d-flex justify-content-end\">\r\n                          <Button variant=\"outline-warning\" size=\"sm\" onClick={handleLocationRequest}>\r\n                            Retry Location Access\r\n                          </Button>\r\n                        </div>\r\n                      </Alert>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {cameraActive && (\r\n                <div className=\"webcam-container mb-3\">\r\n                  <Webcam\r\n                    audio={false}\r\n                    ref={webcamRef}\r\n                    screenshotFormat=\"image/jpeg\"\r\n                    className=\"webcam\"\r\n                    videoConstraints={{\r\n                      width: 640,\r\n                      height: 480,\r\n                      facingMode: cameraOrientation\r\n                    }}\r\n                  />\r\n                  {isMobile && (\r\n                    <Button \r\n                      variant=\"outline-secondary\" \r\n                      onClick={toggleCameraOrientation}\r\n                      className=\"mt-2 mb-2\"\r\n                      size=\"sm\"\r\n                    >\r\n                      Rotate Camera\r\n                    </Button>\r\n                  )}\r\n                  <Button \r\n                    variant=\"success\" \r\n                    onClick={handleCapture}\r\n                    className=\"mt-2\"\r\n                  >\r\n                    Capture Photo\r\n                  </Button>\r\n                </div>\r\n              )}\r\n\r\n              {Object.keys(imagePreviewsMap).length > 0 && (\r\n                <div className=\"image-preview-container mb-3\">\r\n                  <h5>Previews</h5>\r\n                  <div className=\"image-gallery\">\r\n                    {Object.entries(imagePreviewsMap).map(([name, preview], index) => (\r\n                      <div \r\n                        key={name} \r\n                        className={`image-thumbnail ${index === currentImageIndex ? 'selected' : ''}`}\r\n                        onClick={() => setCurrentImageIndex(index)}\r\n                      >\r\n                        <img \r\n                          src={preview} \r\n                          alt={`Preview ${index + 1}`} \r\n                          className=\"img-thumbnail\" \r\n                        />\r\n                        <button \r\n                          className=\"btn btn-sm btn-danger remove-image\" \r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            const newFiles = imageFiles.filter((_, i) => i !== index);\r\n                            const newPreviewsMap = {...imagePreviewsMap};\r\n                            const newLocationMap = {...imageLocationMap};\r\n                            delete newPreviewsMap[name];\r\n                            delete newLocationMap[name];\r\n                            setImageFiles(newFiles);\r\n                            setImagePreviewsMap(newPreviewsMap);\r\n                            setImageLocationMap(newLocationMap);\r\n                            if (currentImageIndex >= newFiles.length) {\r\n                              setCurrentImageIndex(Math.max(0, newFiles.length - 1));\r\n                            }\r\n                          }}\r\n                        >\r\n                          ×\r\n                        </button>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                  <div className=\"current-image-preview\">\r\n                    {Object.values(imagePreviewsMap)[currentImageIndex] && (\r\n                      <img \r\n                        src={Object.values(imagePreviewsMap)[currentImageIndex]} \r\n                        alt=\"Current Preview\" \r\n                        className=\"image-preview img-fluid\" \r\n                      />\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {error && <Alert variant=\"danger\">{error}</Alert>}\r\n\r\n              <div className=\"d-flex gap-2 mb-3\">\r\n                <Button \r\n                  variant=\"primary\" \r\n                  onClick={handleProcess}\r\n                  disabled={Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\r\n                      <span className=\"ms-2\">Detecting...</span>\r\n                    </>\r\n                  ) : (\r\n                    `Detect Current Image`\r\n                  )}\r\n                </Button>\r\n                \r\n                <Button \r\n                  variant=\"success\" \r\n                  onClick={handleProcessAll}\r\n                  disabled={Object.keys(imagePreviewsMap).length === 0 || loading || batchProcessing}\r\n                >\r\n                  {batchProcessing ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" />\r\n                      <span className=\"ms-2\">Processing {processedCount}/{totalToProcess}</span>\r\n                    </>\r\n                  ) : (\r\n                    `Process All Images`\r\n                  )}\r\n                </Button>\r\n                \r\n                <Button \r\n                  variant=\"secondary\" \r\n                  onClick={handleReset}\r\n                  disabled={loading || batchProcessing}\r\n                >\r\n                  Reset\r\n                </Button>\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n\r\n          {processedImage && (\r\n            <Card className=\"mb-4\">\r\n              <Card.Body>\r\n                <h4 className=\"mb-3\">Detection Results</h4>\r\n                \r\n                {/* Location status in results */}\r\n                <div className=\"mb-3\">\r\n                  {(() => {\r\n                    const imageLocation = getCurrentImageLocation();\r\n                    return imageLocation !== 'Not Available' && imageLocation !== 'Location Error' && imageLocation !== 'Permission Denied' ? (\r\n                      <Alert variant=\"success\" className=\"py-2\">\r\n                        <small>\r\n                          <i className=\"fas fa-map-marker-alt me-2\"></i>\r\n                          <strong>Image Location Data:</strong> {imageLocation}\r\n                        </small>\r\n                      </Alert>\r\n                    ) : (\r\n                      <Alert variant=\"warning\" className=\"py-2\">\r\n                        <small>\r\n                          <i className=\"fas fa-exclamation-triangle me-2\"></i>\r\n                          <strong>Location Warning:</strong> Location data was not available for this image. \r\n                          {Object.keys(imagePreviewsMap).length > 0 && Object.keys(imagePreviewsMap)[currentImageIndex].includes('camera_capture') \r\n                            ? 'The camera may have been disabled before capturing, or location access was denied.'\r\n                            : 'Uploaded images do not contain GPS data. Use the live camera for location-tagged captures.'}\r\n                        </small>\r\n                      </Alert>\r\n                    );\r\n                  })()}\r\n                </div>\r\n                \r\n                <div className=\"processed-image-container mb-3\">\r\n                  <img \r\n                    src={processedImage} \r\n                    alt=\"Processed\" \r\n                    className=\"processed-image img-fluid\" \r\n                  />\r\n                </div>\r\n\r\n                {results && (\r\n                  <div className=\"results-summary\">\r\n                    {detectionType === 'all' && (\r\n                      <div className=\"detection-summary-card\">\r\n                        <h4 className=\"mb-4 text-center\">🔍 All Defects Detection Results</h4>\r\n                        \r\n                        {/* Display any error messages for failed models */}\r\n                        {results.model_errors && Object.keys(results.model_errors).length > 0 && (\r\n                          <Alert variant=\"warning\" className=\"mb-3 model-error-alert\">\r\n                            <Alert.Heading as=\"h6\">⚠️ Partial Detection Results</Alert.Heading>\r\n                            <p>Some detection models encountered errors:</p>\r\n                            <ul className=\"mb-0\">\r\n                              {Object.entries(results.model_errors).map(([model, error]) => (\r\n                                <li key={model}><strong>{model}:</strong> {error}</li>\r\n                              ))}\r\n                            </ul>\r\n                          </Alert>\r\n                        )}\r\n                        \r\n                        {/* Potholes Section */}\r\n                        <div className=\"defect-section potholes\">\r\n                          <h5 className=\"text-danger\">\r\n                            <span className=\"emoji\">🕳️</span>\r\n                            Potholes Detected: {results.potholes ? results.potholes.length : 0}\r\n                          </h5>\r\n                          {results.potholes && results.potholes.length > 0 ? (\r\n                            <div className=\"scrollable-table mb-3\">\r\n                              <table className=\"table table-striped table-bordered\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Area (cm²)</th>\r\n                                    <th>Width (cm)</th>\r\n                                    <th>Length (cm)</th>\r\n                                    <th>Max Depth (cm)</th>\r\n                                    <th>Volume (cm³)</th>\r\n                                    <th>Volume Range</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {results.potholes.map((pothole) => (\r\n                                    <tr key={pothole.pothole_id}>\r\n                                      <td>{pothole.pothole_id}</td>\r\n                                      <td>{pothole.area_cm2.toFixed(2)}</td>\r\n                                      <td>{pothole.width_cm ? pothole.width_cm.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{pothole.length_cm ? pothole.length_cm.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{pothole.max_depth_cm ? pothole.max_depth_cm.toFixed(2) : pothole.depth_cm.toFixed(2)}</td>\r\n                                      <td>{pothole.volume.toFixed(2)}</td>\r\n                                      <td>{pothole.volume_range}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No potholes detected in this image.</div>\r\n                          )}\r\n                        </div>\r\n\r\n                        {/* Cracks Section */}\r\n                        <div className=\"defect-section cracks\">\r\n                          <h5 className=\"text-success\">\r\n                            <span className=\"emoji\">🪨</span>\r\n                            Alligator Cracks Detected: {results.cracks ? results.cracks.length : 0}\r\n                          </h5>\r\n                          {results.cracks && results.cracks.length > 0 ? (\r\n                            <>\r\n                              <div className=\"scrollable-table mb-3\">\r\n                                <table className=\"table table-striped table-bordered\">\r\n                                  <thead>\r\n                                    <tr>\r\n                                      <th>ID</th>\r\n                                      <th>Type</th>\r\n                                      <th>Area (cm²)</th>\r\n                                      <th>Area Range</th>\r\n                                    </tr>\r\n                                  </thead>\r\n                                  <tbody>\r\n                                    {results.cracks.map((crack) => (\r\n                                      <tr key={crack.crack_id}>\r\n                                        <td>{crack.crack_id}</td>\r\n                                        <td>{crack.crack_type}</td>\r\n                                        <td>{crack.area_cm2.toFixed(2)}</td>\r\n                                        <td>{crack.area_range}</td>\r\n                                      </tr>\r\n                                    ))}\r\n                                  </tbody>\r\n                                </table>\r\n                              </div>\r\n                              \r\n                              {results.type_counts && (\r\n                                <div>\r\n                                  <h6>Crack Types Summary</h6>\r\n                                  <ul className=\"crack-types-list\">\r\n                                    {Object.entries(results.type_counts).map(([type, count]) => (\r\n                                      count > 0 && (\r\n                                        <li key={type}>\r\n                                          <strong>{type}:</strong> {count}\r\n                                        </li>\r\n                                      )\r\n                                    ))}\r\n                                  </ul>\r\n                                </div>\r\n                              )}\r\n                            </>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No cracks detected in this image.</div>\r\n                          )}\r\n                        </div>\r\n\r\n                        {/* Kerbs Section */}\r\n                        <div className=\"defect-section kerbs\">\r\n                          <h5 className=\"text-primary\">\r\n                            <span className=\"emoji\">🚧</span>\r\n                            Kerbs Detected: {results.kerbs ? results.kerbs.length : 0}\r\n                          </h5>\r\n                          {results.kerbs && results.kerbs.length > 0 ? (\r\n                            <>\r\n                              <div className=\"scrollable-table mb-3\">\r\n                                <table className=\"table table-striped table-bordered\">\r\n                                  <thead>\r\n                                    <tr>\r\n                                      <th>ID</th>\r\n                                      <th>Type</th>\r\n                                      <th>Condition</th>\r\n                                      <th>Length</th>\r\n                                    </tr>\r\n                                  </thead>\r\n                                  <tbody>\r\n                                    {results.kerbs.map((kerb) => (\r\n                                      <tr key={kerb.kerb_id}>\r\n                                        <td>{kerb.kerb_id}</td>\r\n                                        <td>{kerb.kerb_type}</td>\r\n                                        <td>{kerb.condition}</td>\r\n                                        <td>{kerb.length_m.toFixed(2)}</td>\r\n                                      </tr>\r\n                                    ))}\r\n                                  </tbody>\r\n                                </table>\r\n                              </div>\r\n                              \r\n                              {results.condition_counts && (\r\n                                <div>\r\n                                  <h6>Kerb Conditions Summary</h6>\r\n                                  <ul className=\"kerb-types-list\">\r\n                                    {Object.entries(results.condition_counts).map(([condition, count]) => (\r\n                                      count > 0 && (\r\n                                        <li key={condition}>\r\n                                          <strong>{condition}:</strong> {count}\r\n                                        </li>\r\n                                      )\r\n                                    ))}\r\n                                  </ul>\r\n                                </div>\r\n                              )}\r\n                            </>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No kerbs detected in this image.</div>\r\n                          )}\r\n                        </div>\r\n\r\n                        {/* Overall Summary */}\r\n                        <div className=\"summary-stats\">\r\n                          <h6 className=\"mb-3\">📊 Detection Summary</h6>\r\n                          <div className=\"row\">\r\n                            <div className=\"col-md-4 stat-item\">\r\n                              <div className=\"stat-value text-danger\">{results.potholes ? results.potholes.length : 0}</div>\r\n                              <div className=\"stat-label\">Potholes</div>\r\n                            </div>\r\n                            <div className=\"col-md-4 stat-item\">\r\n                              <div className=\"stat-value text-success\">{results.cracks ? results.cracks.length : 0}</div>\r\n                              <div className=\"stat-label\">Cracks</div>\r\n                            </div>\r\n                            <div className=\"col-md-4 stat-item\">\r\n                              <div className=\"stat-value text-primary\">{results.kerbs ? results.kerbs.length : 0}</div>\r\n                              <div className=\"stat-label\">Kerbs</div>\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"text-center mt-3\">\r\n                            <span className=\"total-defects-badge\">\r\n                              Total Defects: {(results.potholes ? results.potholes.length : 0) + (results.cracks ? results.cracks.length : 0) + (results.kerbs ? results.kerbs.length : 0)}\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {detectionType === 'potholes' && results.potholes && (\r\n                      <div>\r\n                        <h5>Detected Potholes: {results.potholes.length}</h5>\r\n                        {results.potholes.length > 0 && (\r\n                          <div className=\"scrollable-table mb-3\">\r\n                            <table className=\"table table-striped table-bordered\">\r\n                              <thead>\r\n                                <tr>\r\n                                  <th>ID</th>\r\n                                  <th>Area (cm²)</th>\r\n                                  <th>Width (cm)</th>\r\n                                  <th>Length (cm)</th>\r\n                                  <th>Max Depth (cm)</th>\r\n                                  <th>Volume (cm³)</th>\r\n                                  <th>Volume Range</th>\r\n                                </tr>\r\n                              </thead>\r\n                              <tbody>\r\n                                {results.potholes.map((pothole) => (\r\n                                  <tr key={pothole.pothole_id}>\r\n                                    <td>{pothole.pothole_id}</td>\r\n                                    <td>{pothole.area_cm2.toFixed(2)}</td>\r\n                                    <td>{pothole.width_cm ? pothole.width_cm.toFixed(2) : 'N/A'}</td>\r\n                                    <td>{pothole.length_cm ? pothole.length_cm.toFixed(2) : 'N/A'}</td>\r\n                                    <td>{pothole.max_depth_cm ? pothole.max_depth_cm.toFixed(2) : pothole.depth_cm.toFixed(2)}</td>\r\n                                    <td>{pothole.volume.toFixed(2)}</td>\r\n                                    <td>{pothole.volume_range}</td>\r\n                                  </tr>\r\n                                ))}\r\n                              </tbody>\r\n                            </table>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n\r\n                    {detectionType === 'cracks' && results.cracks && (\r\n                      <div>\r\n                        <h5>Detected Cracks: {results.cracks.length}</h5>\r\n                        {results.cracks.length > 0 && (\r\n                          <div className=\"scrollable-table mb-3\">\r\n                            <table className=\"table table-striped table-bordered\">\r\n                              <thead>\r\n                                <tr>\r\n                                  <th>ID</th>\r\n                                  <th>Type</th>\r\n                                  <th>Area (cm²)</th>\r\n                                  <th>Area Range</th>\r\n                                </tr>\r\n                              </thead>\r\n                              <tbody>\r\n                                {results.cracks.map((crack) => (\r\n                                  <tr key={crack.crack_id}>\r\n                                    <td>{crack.crack_id}</td>\r\n                                    <td>{crack.crack_type}</td>\r\n                                    <td>{crack.area_cm2.toFixed(2)}</td>\r\n                                    <td>{crack.area_range}</td>\r\n                                  </tr>\r\n                                ))}\r\n                              </tbody>\r\n                            </table>\r\n                          </div>\r\n                        )}\r\n\r\n                        {results.type_counts && (\r\n                          <div>\r\n                            <h5>Crack Types Summary</h5>\r\n                            <ul className=\"crack-types-list\">\r\n                              {Object.entries(results.type_counts).map(([type, count]) => (\r\n                                count > 0 && (\r\n                                  <li key={type}>\r\n                                    <strong>{type}:</strong> {count}\r\n                                  </li>\r\n                                )\r\n                              ))}\r\n                            </ul>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n\r\n                    {detectionType === 'kerbs' && results.kerbs && (\r\n                      <div>\r\n                        <h5>Detected Kerbs: {results.kerbs.length}</h5>\r\n                        {results.kerbs.length > 0 && (\r\n                          <div className=\"scrollable-table mb-3\">\r\n                            <table className=\"table table-striped table-bordered\">\r\n                              <thead>\r\n                                <tr>\r\n                                  <th>ID</th>\r\n                                  <th>Type</th>\r\n                                  <th>Condition</th>\r\n                                  <th>Length</th>\r\n                                </tr>\r\n                              </thead>\r\n                              <tbody>\r\n                                {results.kerbs.map((kerb) => (\r\n                                  <tr key={kerb.kerb_id}>\r\n                                    <td>{kerb.kerb_id}</td>\r\n                                    <td>{kerb.kerb_type}</td>\r\n                                    <td>{kerb.condition}</td>\r\n                                    <td>{kerb.length_m.toFixed(2)}</td>\r\n                                  </tr>\r\n                                ))}\r\n                              </tbody>\r\n                            </table>\r\n                          </div>\r\n                        )}\r\n\r\n                        {results.condition_counts && (\r\n                          <div>\r\n                            <h5>Kerb Conditions Summary</h5>\r\n                            <ul className=\"kerb-conditions-list\">\r\n                              {Object.entries(results.condition_counts).map(([condition, count]) => (\r\n                                count > 0 && (\r\n                                  <li key={condition}>\r\n                                    <strong>{condition}:</strong> {count}\r\n                                  </li>\r\n                                )\r\n                              ))}\r\n                            </ul>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </Card.Body>\r\n            </Card>\r\n          )}\r\n\r\n          {/* Batch processing status indicator */}\r\n          {batchProcessing && (\r\n            <div className=\"batch-processing-status mt-3\">\r\n              <div className=\"d-flex align-items-center\">\r\n                <div className=\"me-3\">\r\n                  <Spinner animation=\"border\" size=\"sm\" role=\"status\" />\r\n                </div>\r\n                <div>\r\n                  <h6 className=\"mb-1\">Processing images: {processedCount}/{totalToProcess}</h6>\r\n                  <div className=\"progress\" style={{ height: '10px' }}>\r\n                    <div \r\n                      className=\"progress-bar\" \r\n                      role=\"progressbar\" \r\n                      style={{ width: `${(processedCount / totalToProcess) * 100}%` }}\r\n                      aria-valuenow={processedCount}\r\n                      aria-valuemin=\"0\" \r\n                      aria-valuemax={totalToProcess}\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {!batchProcessing && batchResults.length > 0 && (\r\n            <div className=\"batch-complete-status mt-3\">\r\n              <Alert variant=\"success\">\r\n                <i className=\"fas fa-check-circle me-2\"></i>\r\n                Processed {batchResults.length} images.\r\n                {batchResults.filter(r => r.success).length} successful,\r\n                {batchResults.filter(r => !r.success && !r.isClassificationError).length} failed.\r\n                {batchResults.filter(r => r.isClassificationError).length > 0 && (\r\n                  <div className=\"mt-2\">\r\n                    <small className=\"text-warning\">\r\n                      <i className=\"fas fa-exclamation-triangle me-1\"></i>\r\n                      {batchResults.filter(r => r.isClassificationError).length} image(s) contained no road.\r\n                    </small>\r\n                  </div>\r\n                )}\r\n              </Alert>\r\n            </div>\r\n          )}\r\n\r\n          {/* Navigation buttons for processed images */}\r\n          {batchResults.length > 1 && !batchProcessing && (\r\n            <div className=\"image-navigation mt-3 d-flex justify-content-between\">\r\n              <Button \r\n                variant=\"outline-secondary\" \r\n                size=\"sm\"\r\n                disabled={currentImageIndex === 0}\r\n                onClick={() => {\r\n                  if (currentImageIndex > 0) {\r\n                    const newIndex = currentImageIndex - 1;\r\n                    setCurrentImageIndex(newIndex);\r\n                    \r\n                    // Find the result for this image\r\n                    const filename = Object.keys(imagePreviewsMap)[newIndex];\r\n                    const result = batchResults.find(r => r.filename === filename);\r\n                    \r\n                    if (result && result.success) {\r\n                      setProcessedImage(result.processedImage);\r\n                      setResults(result.data);\r\n                    } else {\r\n                      setProcessedImage(null);\r\n                      setResults(null);\r\n                    }\r\n                  }\r\n                }}\r\n              >\r\n                <i className=\"fas fa-arrow-left me-1\"></i> Previous Image\r\n              </Button>\r\n              \r\n              <div className=\"image-counter\">\r\n                Image {currentImageIndex + 1} of {Object.keys(imagePreviewsMap).length}\r\n              </div>\r\n              \r\n              <Button \r\n                variant=\"outline-secondary\" \r\n                size=\"sm\"\r\n                disabled={currentImageIndex >= Object.keys(imagePreviewsMap).length - 1}\r\n                onClick={() => {\r\n                  if (currentImageIndex < Object.keys(imagePreviewsMap).length - 1) {\r\n                    const newIndex = currentImageIndex + 1;\r\n                    setCurrentImageIndex(newIndex);\r\n                    \r\n                    // Find the result for this image\r\n                    const filename = Object.keys(imagePreviewsMap)[newIndex];\r\n                    const result = batchResults.find(r => r.filename === filename);\r\n                    \r\n                    if (result && result.success) {\r\n                      setProcessedImage(result.processedImage);\r\n                      setResults(result.data);\r\n                    } else {\r\n                      setProcessedImage(null);\r\n                      setResults(null);\r\n                    }\r\n                  }\r\n                }}\r\n              >\r\n                Next Image <i className=\"fas fa-arrow-right ms-1\"></i>\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </Tab>\r\n        \r\n        <Tab eventKey=\"video\" title=\"Video Detection\">\r\n          <VideoDefectDetection />\r\n        </Tab>\r\n        \r\n        <Tab eventKey=\"information\" title=\"Information\">\r\n          <Card>\r\n            <Card.Body>\r\n              <h4>About Pavement Analysis</h4>\r\n              <p>\r\n                The Pavement Analysis module uses advanced computer vision to detect and analyze \r\n                various types of pavement defects and features:\r\n              </p>\r\n              \r\n              <h5>1. Potholes</h5>\r\n              <p>\r\n                Potholes are bowl-shaped holes of various sizes in the road surface that can be a \r\n                serious hazard to vehicles. The system detects potholes and calculates:\r\n              </p>\r\n              <ul>\r\n                <li>Area in square centimeters</li>\r\n                <li>Depth in centimeters</li>\r\n                <li>Volume</li>\r\n                <li>Classification by size (Small, Medium, Large)</li>\r\n              </ul>\r\n              \r\n              <h5>2. Alligator Cracks</h5>\r\n              <p>\r\n                Alligator cracks are a series of interconnected cracks creating a pattern resembling \r\n                an alligator's scales. These indicate underlying structural weakness. The system \r\n                identifies multiple types of cracks including:\r\n              </p>\r\n              <ul>\r\n                <li>Alligator Cracks</li>\r\n                <li>Edge Cracks</li>\r\n                <li>Hairline Cracks</li>\r\n                <li>Longitudinal Cracks</li>\r\n                <li>Transverse Cracks</li>\r\n              </ul>\r\n              \r\n              <h5>3. Kerbs</h5>\r\n              <p>\r\n                Kerbs are raised edges along a street or path that define boundaries between roadways \r\n                and other areas. The system identifies different kerb conditions including:\r\n              </p>\r\n              <ul>\r\n                <li>Normal/Good Kerbs - Structurally sound and properly visible</li>\r\n                <li>Faded Kerbs - Reduced visibility due to worn paint or weathering</li>\r\n                <li>Damaged Kerbs - Physically damaged or broken kerbs requiring repair</li>\r\n              </ul>\r\n              \r\n              <h5>Location Services & GPS Data</h5>\r\n              <p>\r\n                When using the live camera option, the application can capture GPS coordinates \r\n                to provide precise geolocation data for detected defects. This helps in:\r\n              </p>\r\n              <ul>\r\n                <li>Accurately mapping defect locations</li>\r\n                <li>Creating location-based reports</li>\r\n                <li>Enabling field teams to find specific issues</li>\r\n                <li>Tracking defect patterns by geographic area</li>\r\n              </ul>\r\n              \r\n              <h6>Location Requirements:</h6>\r\n              <ul>\r\n                <li><strong>Secure Connection:</strong> Location services require HTTPS</li>\r\n                <li><strong>Browser Permissions:</strong> You must allow location access when prompted</li>\r\n                <li><strong>Safari Users:</strong> Enable location services in Safari settings</li>\r\n                <li><strong>Mobile Devices:</strong> Ensure location services are enabled in device settings</li>\r\n              </ul>\r\n              \r\n              <div className=\"alert alert-info\">\r\n                <h6><i className=\"fas fa-info-circle me-2\"></i>Troubleshooting Location Issues</h6>\r\n                <p><strong>If location access is denied:</strong></p>\r\n                <ul className=\"mb-2\">\r\n                  <li><strong>Safari:</strong> Settings → Privacy & Security → Location Services</li>\r\n                  <li><strong>Chrome:</strong> Settings → Privacy and security → Site Settings → Location</li>\r\n                  <li><strong>Firefox:</strong> Settings → Privacy & Security → Permissions → Location</li>\r\n                </ul>\r\n                <p><strong>On mobile devices:</strong> Also check your device's location settings and ensure the browser has location permission.</p>\r\n              </div>\r\n\r\n              <h5>How to Use This Module</h5>\r\n              <ol>\r\n                <li>Select the detection type (Potholes, Alligator Cracks, or Kerbs)</li>\r\n                <li>Upload an image or use the camera to capture a photo</li>\r\n                <li>If using the camera, allow location access when prompted for GPS coordinates</li>\r\n                <li>Click the Detect button to analyze the image</li>\r\n                <li>Review the detection results and measurements</li>\r\n              </ol>\r\n              \r\n              <p>\r\n                The detected defects are automatically recorded in the database for tracking \r\n                and analysis in the Dashboard module.\r\n              </p>\r\n            </Card.Body>\r\n          </Card>\r\n        </Tab>\r\n      </Tabs>\r\n\r\n      {/* Classification Error Modal */}\r\n      <Modal\r\n        show={showClassificationModal}\r\n        onHide={() => setShowClassificationModal(false)}\r\n        centered\r\n      >\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            <i className=\"fas fa-exclamation-triangle text-warning me-2\"></i>\r\n            Road Detection Failed\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <div className=\"text-center\">\r\n            <div className=\"mb-3\">\r\n              <i className=\"fas fa-road fa-3x text-muted\"></i>\r\n            </div>\r\n            <h5 className=\"text-danger mb-3\">No Road Detected</h5>\r\n            <p className=\"mb-3\">\r\n              {classificationError || 'The uploaded image does not appear to contain a road. Please upload an image that clearly shows a road surface for defect detection.'}\r\n            </p>\r\n            <div className=\"alert alert-info\">\r\n              <strong>Tips for better results:</strong>\r\n              <ul className=\"mb-0 mt-2 text-start\">\r\n                <li>Ensure the image clearly shows a road surface</li>\r\n                <li>Avoid images with only buildings, sky, or vegetation</li>\r\n                <li>Make sure the road takes up a significant portion of the image</li>\r\n                <li>Use good lighting conditions for clearer road visibility</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button\r\n            variant=\"primary\"\r\n            onClick={() => setShowClassificationModal(false)}\r\n          >\r\n            Try Another Image\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default Pavement; \r\n "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,cAAc,EAAEC,OAAO,EAAEC,KAAK,QAAQ,iBAAiB;AAC1H,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAO,gBAAgB;AACvB,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,oBAAoB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtE,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,eAAe,CAAC;EAC/D,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAAC,aAAa,CAAC;EACzE,MAAM,CAACkD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnD,QAAQ,CAAC,SAAS,CAAC;EACvE,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAAC8D,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACgE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAACoE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACsE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC;EACjE,MAAMwE,iBAAiB,GAAGvE,MAAM,CAAC,IAAI,CAAC;EAEtC,MAAMwE,SAAS,GAAGxE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMyE,YAAY,GAAGzE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAE0E;EAAS,CAAC,GAAG3D,aAAa,CAAC,CAAC;;EAEpC;EACA,MAAM4D,eAAe,gBACnBzD,OAAA,CAACP,OAAO;IAACiE,EAAE,EAAC,kBAAkB;IAACC,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAC1D7D,OAAA,CAACP,OAAO,CAACqE,MAAM;MAACC,EAAE,EAAC,IAAI;MAAAF,QAAA,EAAC;IAA0B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB,CAAC,eACnEnE,OAAA,CAACP,OAAO,CAAC2E,IAAI;MAAAP,QAAA,gBACX7D,OAAA;QAAG2D,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAEpC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJnE,OAAA;QAAI2D,KAAK,EAAE;UAAEU,YAAY,EAAE,GAAG;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACpD7D,OAAA;UAAA6D,QAAA,EAAI;QAAoC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CnE,OAAA;UAAA6D,QAAA,EAAI;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BnE,OAAA;UAAA6D,QAAA,EAAI;QAAkC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3CnE,OAAA;UAAA6D,QAAA,EAAI;QAAmD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACV;;EAED;EACA,MAAMI,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,CAACC,SAAS,CAACC,WAAW,IAAI,CAACD,SAAS,CAACC,WAAW,CAACC,KAAK,EAAE;MAC1D;MACA,OAAO,QAAQ;IACjB;IAEA,IAAI;MACF,MAAMC,UAAU,GAAG,MAAMH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;QAAEE,IAAI,EAAE;MAAc,CAAC,CAAC;MAC7E,OAAOD,UAAU,CAACE,KAAK;IACzB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEF,GAAG,CAAC;MAC5D,OAAO,QAAQ;IACjB;EACF,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC;MACA,IAAI,CAACZ,SAAS,CAACa,WAAW,EAAE;QAC1BD,MAAM,CAAC,IAAIE,KAAK,CAAC,8CAA8C,CAAC,CAAC;QACjE;MACF;;MAEA;MACA,IAAI,CAACC,MAAM,CAACC,eAAe,EAAE;QAC3BJ,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC;QAClE;MACF;MAEA,MAAMG,OAAO,GAAG;QACdC,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QAAE;QAChBC,UAAU,EAAE,KAAK,CAAC;MACpB,CAAC;MAEDpB,SAAS,CAACa,WAAW,CAACQ,kBAAkB,CACrCC,QAAQ,IAAK;QACZX,OAAO,CAACW,QAAQ,CAAC;MACnB,CAAC,EACAvE,KAAK,IAAK;QACT,IAAIwE,YAAY,GAAG,6BAA6B;QAEhD,QAAQxE,KAAK,CAACyE,IAAI;UAChB,KAAKzE,KAAK,CAAC0E,iBAAiB;YAC1BF,YAAY,GAAG,sFAAsF;YACrG;UACF,KAAKxE,KAAK,CAAC2E,oBAAoB;YAC7BH,YAAY,GAAG,wDAAwD;YACvE;UACF,KAAKxE,KAAK,CAAC4E,OAAO;YAChBJ,YAAY,GAAG,+CAA+C;YAC9D;UACF;YACEA,YAAY,GAAG,mBAAmBxE,KAAK,CAAC6E,OAAO,EAAE;YACjD;QACJ;QAEAhB,MAAM,CAAC,IAAIE,KAAK,CAACS,YAAY,CAAC,CAAC;MACjC,CAAC,EACDN,OACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMY,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxCjE,kBAAkB,CAAC,IAAI,CAAC;IACxBF,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAI;MACF;MACA,MAAMoE,eAAe,GAAG,MAAM/B,uBAAuB,CAAC,CAAC;MACvDvC,qBAAqB,CAACsE,eAAe,CAAC;;MAEtC;MACA,IAAIA,eAAe,KAAK,QAAQ,EAAE;QAChC,MAAMC,QAAQ,GAAG,sDAAsD,GACvD,+DAA+D,GAC/D,2CAA2C,GAC3C,4CAA4C,GAC5C,uCAAuC;QACvDrE,gBAAgB,CAACqE,QAAQ,CAAC;QAC1B3E,cAAc,CAAC,mBAAmB,CAAC;QACnC;MACF;;MAEA;MACA,MAAMkE,QAAQ,GAAG,MAAMb,eAAe,CAAC,CAAC;MACxC,MAAM;QAAEuB,QAAQ;QAAEC;MAAU,CAAC,GAAGX,QAAQ,CAACY,MAAM;;MAE/C;MACA,MAAMC,eAAe,GAAG,GAAGH,QAAQ,CAACI,OAAO,CAAC,CAAC,CAAC,KAAKH,SAAS,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MACzEhF,cAAc,CAAC+E,eAAe,CAAC;MAC/B3E,qBAAqB,CAAC,SAAS,CAAC;MAChCE,gBAAgB,CAAC,EAAE,CAAC;MAEpB6C,OAAO,CAAC8B,GAAG,CAAC,oBAAoB,EAAE;QAAEL,QAAQ;QAAEC,SAAS;QAAEK,QAAQ,EAAEhB,QAAQ,CAACY,MAAM,CAACI;MAAS,CAAC,CAAC;IAEhG,CAAC,CAAC,OAAOvF,KAAK,EAAE;MACdwD,OAAO,CAACxD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDW,gBAAgB,CAACX,KAAK,CAAC6E,OAAO,CAAC;MAC/BxE,cAAc,CAAC,gBAAgB,CAAC;;MAEhC;MACA,IAAIL,KAAK,CAAC6E,OAAO,CAACW,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACpC/E,qBAAqB,CAAC,QAAQ,CAAC;MACjC;IACF,CAAC,SAAS;MACRI,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM4E,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK,CAAC;IACxC,IAAIA,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACpB5G,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE,GAAGyG,KAAK,CAAC,CAAC;;MAExC;MACAA,KAAK,CAACK,OAAO,CAACC,IAAI,IAAI;QACpB,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;UACvB/G,mBAAmB,CAACgH,IAAI,KAAK;YAC3B,GAAGA,IAAI;YACP,CAACJ,IAAI,CAAC5C,IAAI,GAAG6C,MAAM,CAACI;UACtB,CAAC,CAAC,CAAC;QACL,CAAC;QACDJ,MAAM,CAACK,aAAa,CAACN,IAAI,CAAC;;QAE1B;QACA1G,mBAAmB,CAAC8G,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP,CAACJ,IAAI,CAAC5C,IAAI,GAAG;QACf,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;;MAEF;MACA1D,iBAAiB,CAAC,IAAI,CAAC;MACvBE,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMuG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMC,QAAQ,GAAG1E,SAAS,CAAC2E,OAAO,CAACC,aAAa,CAAC,CAAC;IAClD,IAAIF,QAAQ,EAAE;MACZ;MACA,IAAIrG,WAAW,KAAK,eAAe,IAAIA,WAAW,KAAK,gBAAgB,EAAE;QACvE,MAAM0E,qBAAqB,CAAC,CAAC;MAC/B;MAEA,MAAM8B,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC1C,MAAMC,QAAQ,GAAG,kBAAkBH,SAAS,MAAM;MAClD,MAAMI,kBAAkB,GAAG5G,WAAW,CAAC,CAAC;;MAExCjB,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE6H,QAAQ,CAAC,CAAC;MACxC1H,mBAAmB,CAACgH,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP,CAACU,QAAQ,GAAGN;MACd,CAAC,CAAC,CAAC;MACHlH,mBAAmB,CAAC8G,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP,CAACU,QAAQ,GAAGC;MACd,CAAC,CAAC,CAAC;MACHvH,oBAAoB,CAACP,UAAU,CAAC6G,MAAM,CAAC;MAEvCpG,iBAAiB,CAAC,IAAI,CAAC;MACvBE,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACAuD,OAAO,CAAC8B,GAAG,CAAC,kCAAkC,EAAE0B,kBAAkB,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIC,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAO3F,WAAW,CAAC,CAAC;IACtB;IAEA,MAAMgH,eAAe,GAAGF,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAACI,iBAAiB,CAAC;IACxE,OAAOF,gBAAgB,CAAC8H,eAAe,CAAC,IAAI,eAAe;EAC7D,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMC,cAAc,GAAG,CAACpH,YAAY;IACpCC,eAAe,CAACmH,cAAc,CAAC;IAE/B,IAAIA,cAAc,EAAE;MAClB;MACA,MAAMxC,qBAAqB,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL;MACA;MACA,IAAIoC,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,KAAK,CAAC,EAAE;QAC9C1F,cAAc,CAAC,eAAe,CAAC;QAC/BM,gBAAgB,CAAC,EAAE,CAAC;QACpBF,qBAAqB,CAAC,SAAS,CAAC;MAClC;IACF;EACF,CAAC;;EAED;EACA,MAAM8G,uBAAuB,GAAGA,CAAA,KAAM;IACpChH,oBAAoB,CAAC8F,IAAI,IAAIA,IAAI,KAAK,aAAa,GAAG,MAAM,GAAG,aAAa,CAAC;EAC/E,CAAC;;EAED;EACA,MAAMmB,yBAAyB,GAAIhD,YAAY,IAAK;IAClDjD,sBAAsB,CAACiD,YAAY,CAAC;IACpCnD,0BAA0B,CAAC,IAAI,CAAC;IAChCpB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMwH,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC1H,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAMyH,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;MACjD,MAAMC,IAAI,GAAGH,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,IAAI;;MAEvD;MACA,MAAMM,mBAAmB,GAAGd,MAAM,CAACe,MAAM,CAAC7I,gBAAgB,CAAC,CAACI,iBAAiB,CAAC;MAE9E,IAAI,CAACwI,mBAAmB,EAAE;QACxB/H,QAAQ,CAAC,kCAAkC,CAAC;QAC5CF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMmI,gBAAgB,GAAGjB,uBAAuB,CAAC,CAAC;;MAElD;MACA,MAAMkB,WAAW,GAAG;QAClBC,KAAK,EAAEJ,mBAAmB;QAC1B5H,WAAW,EAAE8H,gBAAgB;QAC7BG,QAAQ,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,KAAI,SAAS;QACrCC,IAAI,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,KAAI;MACtB,CAAC;;MAED;MACA,IAAIC,QAAQ;MACZ,QAAOvJ,aAAa;QAClB,KAAK,KAAK;UACRuJ,QAAQ,GAAG,0BAA0B;UACrC;QACF,KAAK,UAAU;UACbA,QAAQ,GAAG,+BAA+B;UAC1C;QACF,KAAK,QAAQ;UACXA,QAAQ,GAAG,6BAA6B;UACxC;QACF,KAAK,OAAO;UACVA,QAAQ,GAAG,4BAA4B;UACvC;QACF;UACEA,QAAQ,GAAG,0BAA0B;MACzC;;MAEA;MACA,MAAMC,QAAQ,GAAG,MAAMpK,KAAK,CAACqK,IAAI,CAACF,QAAQ,EAAEJ,WAAW,CAAC;;MAExD;MACA,IAAIK,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBhJ,iBAAiB,CAAC6I,QAAQ,CAACE,IAAI,CAACE,eAAe,CAAC;QAChD/I,UAAU,CAAC2I,QAAQ,CAACE,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLzI,QAAQ,CAACuI,QAAQ,CAACE,IAAI,CAAC7D,OAAO,IAAI,kBAAkB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO7E,KAAK,EAAE;MAAA,IAAA6I,eAAA,EAAAC,oBAAA;MACd,MAAMtE,YAAY,GAAG,EAAAqE,eAAA,GAAA7I,KAAK,CAACwI,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBH,IAAI,cAAAI,oBAAA,uBAApBA,oBAAA,CAAsBjE,OAAO,KAAI,uDAAuD;;MAE7G;MACA,IAAIL,YAAY,CAACgB,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC7CgC,yBAAyB,CAAChD,YAAY,CAAC;MACzC,CAAC,MAAM;QACLvE,QAAQ,CAACuE,YAAY,CAAC;MACxB;IACF,CAAC,SAAS;MACRzE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgJ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI7B,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,KAAK,CAAC,EAAE;MAC9C9F,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEAgB,kBAAkB,CAAC,IAAI,CAAC;IACxBhB,QAAQ,CAAC,EAAE,CAAC;IACZc,eAAe,CAAC,EAAE,CAAC;IACnBI,iBAAiB,CAAC,CAAC,CAAC;IACpBM,iBAAiB,CAACyF,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,CAAC;;IAEvD;IACA,MAAM2B,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;IACjD,MAAMC,IAAI,GAAGH,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,IAAI;IAEvD,IAAI;MACF;MACA,IAAIa,QAAQ;MACZ,QAAOvJ,aAAa;QAClB,KAAK,KAAK;UACRuJ,QAAQ,GAAG,0BAA0B;UACrC;QACF,KAAK,UAAU;UACbA,QAAQ,GAAG,+BAA+B;UAC1C;QACF,KAAK,QAAQ;UACXA,QAAQ,GAAG,6BAA6B;UACxC;QACF,KAAK,OAAO;UACVA,QAAQ,GAAG,4BAA4B;UACvC;QACF;UACEA,QAAQ,GAAG,0BAA0B;MACzC;MAEA,MAAM3I,OAAO,GAAG,EAAE;MAClB,MAAMoJ,SAAS,GAAG9B,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC;;MAE/C;MACA,KAAK,IAAI6J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACjD,MAAM,EAAEkD,CAAC,EAAE,EAAE;QACzC,MAAMlC,QAAQ,GAAGiC,SAAS,CAACC,CAAC,CAAC;QAC7B,MAAMC,SAAS,GAAG9J,gBAAgB,CAAC2H,QAAQ,CAAC;QAE5C,IAAI;UACF;UACAtH,oBAAoB,CAACwJ,CAAC,CAAC;;UAEvB;UACA,MAAMf,gBAAgB,GAAG5I,gBAAgB,CAACyH,QAAQ,CAAC,IAAI,eAAe;;UAEtE;UACA,MAAMoB,WAAW,GAAG;YAClBC,KAAK,EAAEc,SAAS;YAChB9I,WAAW,EAAE8H,gBAAgB;YAC7BG,QAAQ,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,KAAI,SAAS;YACrCC,IAAI,EAAE,CAAAT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,KAAI;UACtB,CAAC;;UAED;UACA,MAAME,QAAQ,GAAG,MAAMpK,KAAK,CAACqK,IAAI,CAACF,QAAQ,EAAEJ,WAAW,CAAC;UAExD,IAAIK,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;YACzB;YACAhJ,iBAAiB,CAAC6I,QAAQ,CAACE,IAAI,CAACE,eAAe,CAAC;YAChD/I,UAAU,CAAC2I,QAAQ,CAACE,IAAI,CAAC;YAEzB9I,OAAO,CAACuJ,IAAI,CAAC;cACXpC,QAAQ;cACR4B,OAAO,EAAE,IAAI;cACbjJ,cAAc,EAAE8I,QAAQ,CAACE,IAAI,CAACE,eAAe;cAC7CF,IAAI,EAAEF,QAAQ,CAACE;YACjB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,MAAMlE,YAAY,GAAGgE,QAAQ,CAACE,IAAI,CAAC7D,OAAO,IAAI,kBAAkB;YAChEjF,OAAO,CAACuJ,IAAI,CAAC;cACXpC,QAAQ;cACR4B,OAAO,EAAE,KAAK;cACd3I,KAAK,EAAEwE,YAAY;cACnB4E,qBAAqB,EAAE5E,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;YACjE,CAAC,CAAC;UACJ;QACF,CAAC,CAAC,OAAOxF,KAAK,EAAE;UAAA,IAAAqJ,gBAAA,EAAAC,qBAAA;UACd,MAAM9E,YAAY,GAAG,EAAA6E,gBAAA,GAAArJ,KAAK,CAACwI,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBzE,OAAO,KAAI,oCAAoC;UAC1FjF,OAAO,CAACuJ,IAAI,CAAC;YACXpC,QAAQ;YACR4B,OAAO,EAAE,KAAK;YACd3I,KAAK,EAAEwE,YAAY;YACnB4E,qBAAqB,EAAE5E,YAAY,CAACgB,QAAQ,CAAC,kBAAkB;UACjE,CAAC,CAAC;QACJ;;QAEA;QACArE,iBAAiB,CAACkF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;;QAEnC;QACA;QACA,IAAI4C,CAAC,GAAGD,SAAS,CAACjD,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM,IAAIpC,OAAO,CAACC,OAAO,IAAI2F,UAAU,CAAC3F,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3D;MACF;;MAEA;MACA7C,eAAe,CAACnB,OAAO,CAAC;IAE1B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,QAAQ,CAAC,2BAA2B,IAAID,KAAK,CAAC6E,OAAO,IAAI,eAAe,CAAC,CAAC;IAC5E,CAAC,SAAS;MACR5D,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMuI,WAAW,GAAGA,CAAA,KAAM;IACxBrK,aAAa,CAAC,EAAE,CAAC;IACjBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACvBE,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACvBE,oBAAoB,CAAC,CAAC,CAAC;IACvBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,EAAE,CAAC;IACZc,eAAe,CAAC,EAAE,CAAC;IACnBI,iBAAiB,CAAC,CAAC,CAAC;IACpBM,iBAAiB,CAAC,CAAC,CAAC;;IAEpB;IACApB,cAAc,CAAC,eAAe,CAAC;IAC/BM,gBAAgB,CAAC,EAAE,CAAC;IACpBF,qBAAqB,CAAC,SAAS,CAAC;IAEhC,IAAIuB,YAAY,CAAC0E,OAAO,EAAE;MACxB1E,YAAY,CAAC0E,OAAO,CAAC+C,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI5I,YAAY,CAACiF,MAAM,KAAK,CAAC,EAAE;;IAE/B;IACA,MAAM4D,iBAAiB,GAAG7I,YAAY,CAAC8I,MAAM,CAACtD,MAAM,IAAIA,MAAM,CAACqC,OAAO,CAAC;IACvE,IAAIgB,iBAAiB,CAAC5D,MAAM,KAAK,CAAC,EAAE;IAEpCpE,uBAAuB,CAAC,IAAI,CAAC;IAC7BE,sBAAsB,CAAC,CAAC,CAAC;;IAEzB;IACA,MAAMgI,WAAW,GAAGF,iBAAiB,CAAC,CAAC,CAAC;IACxC,MAAMG,SAAS,GAAG5C,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2K,SAAS,CACvDhD,QAAQ,IAAIA,QAAQ,KAAK8C,WAAW,CAAC9C,QACvC,CAAC;IAED,IAAI+C,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBrK,oBAAoB,CAACqK,SAAS,CAAC;MAC/BnK,iBAAiB,CAACkK,WAAW,CAACnK,cAAc,CAAC;MAC7CG,UAAU,CAACgK,WAAW,CAACnB,IAAI,CAAC;IAC9B;;IAEA;IACA5G,iBAAiB,CAAC4E,OAAO,GAAGsD,WAAW,CAAC,MAAM;MAC5CnI,sBAAsB,CAACoI,SAAS,IAAI;QAClC,MAAMC,SAAS,GAAGD,SAAS,GAAG,CAAC;;QAE/B;QACA,IAAIC,SAAS,IAAIP,iBAAiB,CAAC5D,MAAM,EAAE;UACzCoE,aAAa,CAACrI,iBAAiB,CAAC4E,OAAO,CAAC;UACxC/E,uBAAuB,CAAC,KAAK,CAAC;UAC9B,OAAOsI,SAAS;QAClB;;QAEA;QACA,MAAMG,UAAU,GAAGT,iBAAiB,CAACO,SAAS,CAAC;QAC/C,MAAMG,aAAa,GAAGnD,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2K,SAAS,CAC3DhD,QAAQ,IAAIA,QAAQ,KAAKqD,UAAU,CAACrD,QACtC,CAAC;QAED,IAAIsD,aAAa,KAAK,CAAC,CAAC,EAAE;UACxB5K,oBAAoB,CAAC4K,aAAa,CAAC;UACnC1K,iBAAiB,CAACyK,UAAU,CAAC1K,cAAc,CAAC;UAC5CG,UAAU,CAACuK,UAAU,CAAC1B,IAAI,CAAC;QAC7B;QAEA,OAAOwB,SAAS;MAClB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED;EACA1M,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIsE,iBAAiB,CAAC4E,OAAO,EAAE;QAC7ByD,aAAa,CAACrI,iBAAiB,CAAC4E,OAAO,CAAC;MAC1C;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlJ,SAAS,CAAC,MAAM;IACd,IAAI0C,YAAY,IAAIM,kBAAkB,KAAK,SAAS,EAAE;MACpD;MACAsE,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC5E,YAAY,CAAC,CAAC;;EAElB;EACA1C,SAAS,CAAC,MAAM;IACd,IAAI8M,iBAAiB,GAAG,IAAI;IAE5B,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,IAAItH,SAAS,CAACC,WAAW,IAAID,SAAS,CAACC,WAAW,CAACC,KAAK,EAAE;UACxD,MAAMC,UAAU,GAAG,MAAMH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;YAAEE,IAAI,EAAE;UAAc,CAAC,CAAC;UAE7EiH,iBAAiB,GAAGA,CAAA,KAAM;YACxB7J,qBAAqB,CAAC2C,UAAU,CAACE,KAAK,CAAC;YACvC,IAAIF,UAAU,CAACE,KAAK,KAAK,SAAS,IAAIpD,YAAY,IAAIE,WAAW,KAAK,eAAe,EAAE;cACrF0E,qBAAqB,CAAC,CAAC;YACzB;UACF,CAAC;UAED1B,UAAU,CAACoH,gBAAgB,CAAC,QAAQ,EAAEF,iBAAiB,CAAC;QAC1D;MACF,CAAC,CAAC,OAAO/G,GAAG,EAAE;QACZC,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEF,GAAG,CAAC;MACzD;IACF,CAAC;IAEDgH,gBAAgB,CAAC,CAAC;IAElB,OAAO,MAAM;MACX,IAAID,iBAAiB,EAAE;QACrB,IAAI;UACF,MAAMlH,UAAU,GAAGH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;YAAEE,IAAI,EAAE;UAAc,CAAC,CAAC;UACvED,UAAU,CAACqH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,mBAAmB,CAAC,QAAQ,EAAEL,iBAAiB,CAAC,CAAC;QAC1E,CAAC,CAAC,OAAO/G,GAAG,EAAE;UACZC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEF,GAAG,CAAC;QAC1D;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACrD,YAAY,EAAEE,WAAW,CAAC,CAAC;;EAE/B;EACA5C,SAAS,CAAC,MAAM;IACd;IACA;EAAA,CACD,EAAE,CAACgC,iBAAiB,EAAEF,gBAAgB,CAAC,CAAC;;EAEzC;EACA,MAAMsL,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI9I,iBAAiB,CAAC4E,OAAO,EAAE;MAC7ByD,aAAa,CAACrI,iBAAiB,CAAC4E,OAAO,CAAC;MACxC/E,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;EAED,oBACElD,OAAA,CAAChB,SAAS;IAACoN,SAAS,EAAC,eAAe;IAAAvI,QAAA,gBAClC7D,OAAA;MAAIoM,SAAS,EAAC,eAAe;MAAAvI,QAAA,EAAC;IAAiB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEpDnE,OAAA,CAACZ,IAAI;MACHiN,SAAS,EAAEhM,SAAU;MACrBiM,QAAQ,EAAGC,CAAC,IAAKjM,YAAY,CAACiM,CAAC,CAAE;MACjCH,SAAS,EAAC,MAAM;MAAAvI,QAAA,gBAEhB7D,OAAA,CAACX,GAAG;QAACmN,QAAQ,EAAC,WAAW;QAACC,KAAK,EAAC,iBAAiB;QAAA5I,QAAA,gBAC/C7D,OAAA,CAACf,IAAI;UAACmN,SAAS,EAAC,MAAM;UAAAvI,QAAA,eACpB7D,OAAA,CAACf,IAAI,CAACmF,IAAI;YAAAP,QAAA,gBACR7D,OAAA,CAACb,IAAI,CAACuN,KAAK;cAACN,SAAS,EAAC,MAAM;cAAAvI,QAAA,gBAC1B7D,OAAA,CAACb,IAAI,CAACwN,KAAK;gBAAA9I,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvCnE,OAAA,CAACb,IAAI,CAACyN,MAAM;gBACV5B,KAAK,EAAEzK,aAAc;gBACrBsM,QAAQ,EAAG5F,CAAC,IAAKzG,gBAAgB,CAACyG,CAAC,CAACI,MAAM,CAAC2D,KAAK,CAAE;gBAAAnH,QAAA,gBAElD7D,OAAA;kBAAQgL,KAAK,EAAC,KAAK;kBAAAnH,QAAA,EAAC;gBAA+B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5DnE,OAAA;kBAAQgL,KAAK,EAAC,UAAU;kBAAAnH,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnE,OAAA;kBAAQgL,KAAK,EAAC,QAAQ;kBAAAnH,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDnE,OAAA;kBAAQgL,KAAK,EAAC,OAAO;kBAAAnH,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGXnE,OAAA,CAACR,cAAc;cACbsN,OAAO,EAAC,OAAO;cACfC,SAAS,EAAC,OAAO;cACjBC,OAAO,EAAEvJ,eAAgB;cACzBwJ,SAAS;cAAApJ,QAAA,eAET7D,OAAA;gBACEoM,SAAS,EAAC,uBAAuB;gBACjCzI,KAAK,EAAE;kBAAEuJ,MAAM,EAAE,SAAS;kBAAEC,OAAO,EAAE;gBAAe,CAAE;gBAAAtJ,QAAA,eAEtD7D,OAAA;kBACEoN,GAAG,EAAC,mBAAmB;kBACvBC,GAAG,EAAC,yBAAyB;kBAC7B1J,KAAK,EAAE;oBAAE2J,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE;kBAAO;gBAAE;kBAAAvJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC,eAEnBnE,OAAA;cAAKoM,SAAS,EAAC,MAAM;cAAAvI,QAAA,gBACnB7D,OAAA,CAACb,IAAI,CAACwN,KAAK;gBAAA9I,QAAA,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCnE,OAAA;gBAAKoM,SAAS,EAAC,mBAAmB;gBAAAvI,QAAA,gBAChC7D,OAAA,CAACd,MAAM;kBACLsO,OAAO,EAAE/L,YAAY,GAAG,SAAS,GAAG,iBAAkB;kBACtDgM,OAAO,EAAE7E,YAAa;kBACtB8E,QAAQ,EAAEvL,eAAgB;kBAAA0B,QAAA,EAEzB1B,eAAe,gBACdnC,OAAA,CAAAE,SAAA;oBAAA2D,QAAA,gBACE7D,OAAA,CAACT,OAAO;sBAACwE,EAAE,EAAC,MAAM;sBAAC4J,SAAS,EAAC,QAAQ;sBAACC,IAAI,EAAC,IAAI;sBAAC/D,IAAI,EAAC,QAAQ;sBAAC,eAAY;oBAAM;sBAAA7F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACnFnE,OAAA;sBAAMoM,SAAS,EAAC,MAAM;sBAAAvI,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACjD,CAAC,GAEH1C,YAAY,GAAG,gBAAgB,GAAG;gBACnC;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACTnE,OAAA;kBAAKoM,SAAS,EAAC,sBAAsB;kBAAAvI,QAAA,eACnC7D,OAAA;oBAAOoM,SAAS,EAAC,kBAAkB;oBAAAvI,QAAA,GAAC,cAElC,eAAA7D,OAAA;sBACE6N,IAAI,EAAC,MAAM;sBACXzB,SAAS,EAAC,YAAY;sBACtB0B,MAAM,EAAC,SAAS;sBAChBjB,QAAQ,EAAE7F,gBAAiB;sBAC3B+G,GAAG,EAAExK,YAAa;sBAClBmK,QAAQ,EAAEjM,YAAa;sBACvBuM,QAAQ;oBAAA;sBAAAhK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL1C,YAAY,iBACXzB,OAAA;gBAAKoM,SAAS,EAAC,sBAAsB;gBAAAvI,QAAA,gBACnC7D,OAAA;kBAAOoM,SAAS,EAAC,YAAY;kBAAAvI,QAAA,gBAC3B7D,OAAA;oBAAA6D,QAAA,EAAQ;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAChCpC,kBAAkB,KAAK,SAAS,iBAAI/B,OAAA;oBAAMoM,SAAS,EAAC,mBAAmB;oBAAAvI,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACxFpC,kBAAkB,KAAK,QAAQ,iBAAI/B,OAAA;oBAAMoM,SAAS,EAAC,kBAAkB;oBAAAvI,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACrFpC,kBAAkB,KAAK,QAAQ,iBAAI/B,OAAA;oBAAMoM,SAAS,EAAC,mBAAmB;oBAAAvI,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAC7FpC,kBAAkB,KAAK,SAAS,iBAAI/B,OAAA;oBAAMoM,SAAS,EAAC,qBAAqB;oBAAAvI,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC,EACP,CAACxC,WAAW,KAAK,eAAe,IAAI8G,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,GAAG,CAAC,kBAC3EtH,OAAA;kBAAKoM,SAAS,EAAC,MAAM;kBAAAvI,QAAA,gBACnB7D,OAAA;oBAAOoM,SAAS,EAAC,YAAY;oBAAAvI,QAAA,gBAC3B7D,OAAA;sBAAA6D,QAAA,EAAQ;oBAAiB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAAnE,OAAA;sBAAMoM,SAAS,EAAC,cAAc;sBAAAvI,QAAA,EAAElC;oBAAW;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,EACPsE,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,GAAG,CAAC,iBACvCtH,OAAA;oBAAKoM,SAAS,EAAC,MAAM;oBAAAvI,QAAA,eACnB7D,OAAA;sBAAOoM,SAAS,EAAC,YAAY;sBAAAvI,QAAA,gBAC3B7D,OAAA;wBAAA6D,QAAA,EAAQ;sBAAwB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,eAAAnE,OAAA;wBAAMoM,SAAS,EAAC,cAAc;wBAAAvI,QAAA,EAAE2E,uBAAuB,CAAC;sBAAC;wBAAAxE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EACAlC,aAAa,iBACZjC,OAAA,CAACV,KAAK;kBAACkO,OAAO,EAAC,SAAS;kBAACpB,SAAS,EAAC,WAAW;kBAACzI,KAAK,EAAE;oBAAEsK,QAAQ,EAAE;kBAAW,CAAE;kBAAApK,QAAA,gBAC7E7D,OAAA,CAACV,KAAK,CAAC4O,OAAO;oBAACnK,EAAE,EAAC,IAAI;oBAAAF,QAAA,EAAC;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC,eAC5DnE,OAAA;oBAAK2D,KAAK,EAAE;sBAAEwK,UAAU,EAAE;oBAAW,CAAE;oBAAAtK,QAAA,EAAE5B;kBAAa;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DnE,OAAA;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNnE,OAAA;oBAAKoM,SAAS,EAAC,4BAA4B;oBAAAvI,QAAA,eACzC7D,OAAA,CAACd,MAAM;sBAACsO,OAAO,EAAC,iBAAiB;sBAACI,IAAI,EAAC,IAAI;sBAACH,OAAO,EAAEpH,qBAAsB;sBAAAxC,QAAA,EAAC;oBAE5E;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAEL1C,YAAY,iBACXzB,OAAA;cAAKoM,SAAS,EAAC,uBAAuB;cAAAvI,QAAA,gBACpC7D,OAAA,CAACJ,MAAM;gBACLwO,KAAK,EAAE,KAAM;gBACbL,GAAG,EAAEzK,SAAU;gBACf+K,gBAAgB,EAAC,YAAY;gBAC7BjC,SAAS,EAAC,QAAQ;gBAClBkC,gBAAgB,EAAE;kBAChBhB,KAAK,EAAE,GAAG;kBACVC,MAAM,EAAE,GAAG;kBACXgB,UAAU,EAAE1M;gBACd;cAAE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDX,QAAQ,iBACPxD,OAAA,CAACd,MAAM;gBACLsO,OAAO,EAAC,mBAAmB;gBAC3BC,OAAO,EAAE3E,uBAAwB;gBACjCsD,SAAS,EAAC,WAAW;gBACrBwB,IAAI,EAAC,IAAI;gBAAA/J,QAAA,EACV;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,eACDnE,OAAA,CAACd,MAAM;gBACLsO,OAAO,EAAC,SAAS;gBACjBC,OAAO,EAAE1F,aAAc;gBACvBqE,SAAS,EAAC,MAAM;gBAAAvI,QAAA,EACjB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAEAsE,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,GAAG,CAAC,iBACvCtH,OAAA;cAAKoM,SAAS,EAAC,8BAA8B;cAAAvI,QAAA,gBAC3C7D,OAAA;gBAAA6D,QAAA,EAAI;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBnE,OAAA;gBAAKoM,SAAS,EAAC,eAAe;gBAAAvI,QAAA,EAC3B4E,MAAM,CAAC+F,OAAO,CAAC7N,gBAAgB,CAAC,CAAC8N,GAAG,CAAC,CAAC,CAAC7J,IAAI,EAAE8J,OAAO,CAAC,EAAEC,KAAK,kBAC3D3O,OAAA;kBAEEoM,SAAS,EAAE,mBAAmBuC,KAAK,KAAK5N,iBAAiB,GAAG,UAAU,GAAG,EAAE,EAAG;kBAC9E0M,OAAO,EAAEA,CAAA,KAAMzM,oBAAoB,CAAC2N,KAAK,CAAE;kBAAA9K,QAAA,gBAE3C7D,OAAA;oBACEoN,GAAG,EAAEsB,OAAQ;oBACbrB,GAAG,EAAE,WAAWsB,KAAK,GAAG,CAAC,EAAG;oBAC5BvC,SAAS,EAAC;kBAAe;oBAAApI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACFnE,OAAA;oBACEoM,SAAS,EAAC,oCAAoC;oBAC9CqB,OAAO,EAAGxG,CAAC,IAAK;sBACdA,CAAC,CAAC2H,eAAe,CAAC,CAAC;sBACnB,MAAMC,QAAQ,GAAGpO,UAAU,CAAC0K,MAAM,CAAC,CAAC2D,CAAC,EAAEtE,CAAC,KAAKA,CAAC,KAAKmE,KAAK,CAAC;sBACzD,MAAMI,cAAc,GAAG;wBAAC,GAAGpO;sBAAgB,CAAC;sBAC5C,MAAMqO,cAAc,GAAG;wBAAC,GAAGnO;sBAAgB,CAAC;sBAC5C,OAAOkO,cAAc,CAACnK,IAAI,CAAC;sBAC3B,OAAOoK,cAAc,CAACpK,IAAI,CAAC;sBAC3BlE,aAAa,CAACmO,QAAQ,CAAC;sBACvBjO,mBAAmB,CAACmO,cAAc,CAAC;sBACnCjO,mBAAmB,CAACkO,cAAc,CAAC;sBACnC,IAAIjO,iBAAiB,IAAI8N,QAAQ,CAACvH,MAAM,EAAE;wBACxCtG,oBAAoB,CAACiO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,QAAQ,CAACvH,MAAM,GAAG,CAAC,CAAC,CAAC;sBACxD;oBACF,CAAE;oBAAAzD,QAAA,EACH;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GA3BJS,IAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4BN,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnE,OAAA;gBAAKoM,SAAS,EAAC,uBAAuB;gBAAAvI,QAAA,EACnC4E,MAAM,CAACe,MAAM,CAAC7I,gBAAgB,CAAC,CAACI,iBAAiB,CAAC,iBACjDf,OAAA;kBACEoN,GAAG,EAAE3E,MAAM,CAACe,MAAM,CAAC7I,gBAAgB,CAAC,CAACI,iBAAiB,CAAE;kBACxDsM,GAAG,EAAC,iBAAiB;kBACrBjB,SAAS,EAAC;gBAAyB;kBAAApI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEA5C,KAAK,iBAAIvB,OAAA,CAACV,KAAK;cAACkO,OAAO,EAAC,QAAQ;cAAA3J,QAAA,EAAEtC;YAAK;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEjDnE,OAAA;cAAKoM,SAAS,EAAC,mBAAmB;cAAAvI,QAAA,gBAChC7D,OAAA,CAACd,MAAM;gBACLsO,OAAO,EAAC,SAAS;gBACjBC,OAAO,EAAEzE,aAAc;gBACvB0E,QAAQ,EAAEjF,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,KAAK,CAAC,IAAIjG,OAAO,IAAIkB,eAAgB;gBAAAsB,QAAA,EAElFxC,OAAO,gBACNrB,OAAA,CAAAE,SAAA;kBAAA2D,QAAA,gBACE7D,OAAA,CAACT,OAAO;oBAACwE,EAAE,EAAC,MAAM;oBAAC4J,SAAS,EAAC,QAAQ;oBAACC,IAAI,EAAC,IAAI;oBAAC/D,IAAI,EAAC,QAAQ;oBAAC,eAAY;kBAAM;oBAAA7F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnFnE,OAAA;oBAAMoM,SAAS,EAAC,MAAM;oBAAAvI,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC1C,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAETnE,OAAA,CAACd,MAAM;gBACLsO,OAAO,EAAC,SAAS;gBACjBC,OAAO,EAAEnD,gBAAiB;gBAC1BoD,QAAQ,EAAEjF,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,KAAK,CAAC,IAAIjG,OAAO,IAAIkB,eAAgB;gBAAAsB,QAAA,EAElFtB,eAAe,gBACdvC,OAAA,CAAAE,SAAA;kBAAA2D,QAAA,gBACE7D,OAAA,CAACT,OAAO;oBAACwE,EAAE,EAAC,MAAM;oBAAC4J,SAAS,EAAC,QAAQ;oBAACC,IAAI,EAAC,IAAI;oBAAC/D,IAAI,EAAC,QAAQ;oBAAC,eAAY;kBAAM;oBAAA7F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnFnE,OAAA;oBAAMoM,SAAS,EAAC,MAAM;oBAAAvI,QAAA,GAAC,aAAW,EAACpB,cAAc,EAAC,GAAC,EAACM,cAAc;kBAAA;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,eAC1E,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAETnE,OAAA,CAACd,MAAM;gBACLsO,OAAO,EAAC,WAAW;gBACnBC,OAAO,EAAE1C,WAAY;gBACrB2C,QAAQ,EAAErM,OAAO,IAAIkB,eAAgB;gBAAAsB,QAAA,EACtC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAENlD,cAAc,iBACbjB,OAAA,CAACf,IAAI;UAACmN,SAAS,EAAC,MAAM;UAAAvI,QAAA,eACpB7D,OAAA,CAACf,IAAI,CAACmF,IAAI;YAAAP,QAAA,gBACR7D,OAAA;cAAIoM,SAAS,EAAC,MAAM;cAAAvI,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAG3CnE,OAAA;cAAKoM,SAAS,EAAC,MAAM;cAAAvI,QAAA,EAClB,CAAC,MAAM;gBACN,MAAMsL,aAAa,GAAG3G,uBAAuB,CAAC,CAAC;gBAC/C,OAAO2G,aAAa,KAAK,eAAe,IAAIA,aAAa,KAAK,gBAAgB,IAAIA,aAAa,KAAK,mBAAmB,gBACrHnP,OAAA,CAACV,KAAK;kBAACkO,OAAO,EAAC,SAAS;kBAACpB,SAAS,EAAC,MAAM;kBAAAvI,QAAA,eACvC7D,OAAA;oBAAA6D,QAAA,gBACE7D,OAAA;sBAAGoM,SAAS,EAAC;oBAA4B;sBAAApI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9CnE,OAAA;sBAAA6D,QAAA,EAAQ;oBAAoB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACgL,aAAa;kBAAA;oBAAAnL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAERnE,OAAA,CAACV,KAAK;kBAACkO,OAAO,EAAC,SAAS;kBAACpB,SAAS,EAAC,MAAM;kBAAAvI,QAAA,eACvC7D,OAAA;oBAAA6D,QAAA,gBACE7D,OAAA;sBAAGoM,SAAS,EAAC;oBAAkC;sBAAApI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpDnE,OAAA;sBAAA6D,QAAA,EAAQ;oBAAiB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,oDAClC,EAACsE,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,GAAG,CAAC,IAAImB,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAACI,iBAAiB,CAAC,CAACgG,QAAQ,CAAC,gBAAgB,CAAC,GACpH,oFAAoF,GACpF,4FAA4F;kBAAA;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACR;cACH,CAAC,EAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENnE,OAAA;cAAKoM,SAAS,EAAC,gCAAgC;cAAAvI,QAAA,eAC7C7D,OAAA;gBACEoN,GAAG,EAAEnM,cAAe;gBACpBoM,GAAG,EAAC,WAAW;gBACfjB,SAAS,EAAC;cAA2B;gBAAApI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELhD,OAAO,iBACNnB,OAAA;cAAKoM,SAAS,EAAC,iBAAiB;cAAAvI,QAAA,GAC7BtD,aAAa,KAAK,KAAK,iBACtBP,OAAA;gBAAKoM,SAAS,EAAC,wBAAwB;gBAAAvI,QAAA,gBACrC7D,OAAA;kBAAIoM,SAAS,EAAC,kBAAkB;kBAAAvI,QAAA,EAAC;gBAAgC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAGrEhD,OAAO,CAACiO,YAAY,IAAI3G,MAAM,CAACC,IAAI,CAACvH,OAAO,CAACiO,YAAY,CAAC,CAAC9H,MAAM,GAAG,CAAC,iBACnEtH,OAAA,CAACV,KAAK;kBAACkO,OAAO,EAAC,SAAS;kBAACpB,SAAS,EAAC,wBAAwB;kBAAAvI,QAAA,gBACzD7D,OAAA,CAACV,KAAK,CAAC4O,OAAO;oBAACnK,EAAE,EAAC,IAAI;oBAAAF,QAAA,EAAC;kBAA4B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC,eACnEnE,OAAA;oBAAA6D,QAAA,EAAG;kBAAyC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChDnE,OAAA;oBAAIoM,SAAS,EAAC,MAAM;oBAAAvI,QAAA,EACjB4E,MAAM,CAAC+F,OAAO,CAACrN,OAAO,CAACiO,YAAY,CAAC,CAACX,GAAG,CAAC,CAAC,CAACY,KAAK,EAAE9N,KAAK,CAAC,kBACvDvB,OAAA;sBAAA6D,QAAA,gBAAgB7D,OAAA;wBAAA6D,QAAA,GAASwL,KAAK,EAAC,GAAC;sBAAA;wBAAArL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC5C,KAAK;oBAAA,GAAvC8N,KAAK;sBAAArL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAuC,CACtD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CACR,eAGDnE,OAAA;kBAAKoM,SAAS,EAAC,yBAAyB;kBAAAvI,QAAA,gBACtC7D,OAAA;oBAAIoM,SAAS,EAAC,aAAa;oBAAAvI,QAAA,gBACzB7D,OAAA;sBAAMoM,SAAS,EAAC,OAAO;sBAAAvI,QAAA,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,uBACf,EAAChD,OAAO,CAACmO,QAAQ,GAAGnO,OAAO,CAACmO,QAAQ,CAAChI,MAAM,GAAG,CAAC;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,EACJhD,OAAO,CAACmO,QAAQ,IAAInO,OAAO,CAACmO,QAAQ,CAAChI,MAAM,GAAG,CAAC,gBAC9CtH,OAAA;oBAAKoM,SAAS,EAAC,uBAAuB;oBAAAvI,QAAA,eACpC7D,OAAA;sBAAOoM,SAAS,EAAC,oCAAoC;sBAAAvI,QAAA,gBACnD7D,OAAA;wBAAA6D,QAAA,eACE7D,OAAA;0BAAA6D,QAAA,gBACE7D,OAAA;4BAAA6D,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXnE,OAAA;4BAAA6D,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBnE,OAAA;4BAAA6D,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBnE,OAAA;4BAAA6D,QAAA,EAAI;0BAAW;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACpBnE,OAAA;4BAAA6D,QAAA,EAAI;0BAAc;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACvBnE,OAAA;4BAAA6D,QAAA,EAAI;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACrBnE,OAAA;4BAAA6D,QAAA,EAAI;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRnE,OAAA;wBAAA6D,QAAA,EACG1C,OAAO,CAACmO,QAAQ,CAACb,GAAG,CAAEc,OAAO,iBAC5BvP,OAAA;0BAAA6D,QAAA,gBACE7D,OAAA;4BAAA6D,QAAA,EAAK0L,OAAO,CAACC;0BAAU;4BAAAxL,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC7BnE,OAAA;4BAAA6D,QAAA,EAAK0L,OAAO,CAACE,QAAQ,CAAC7I,OAAO,CAAC,CAAC;0BAAC;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtCnE,OAAA;4BAAA6D,QAAA,EAAK0L,OAAO,CAACG,QAAQ,GAAGH,OAAO,CAACG,QAAQ,CAAC9I,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACjEnE,OAAA;4BAAA6D,QAAA,EAAK0L,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACI,SAAS,CAAC/I,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACnEnE,OAAA;4BAAA6D,QAAA,EAAK0L,OAAO,CAACK,YAAY,GAAGL,OAAO,CAACK,YAAY,CAAChJ,OAAO,CAAC,CAAC,CAAC,GAAG2I,OAAO,CAACM,QAAQ,CAACjJ,OAAO,CAAC,CAAC;0BAAC;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC/FnE,OAAA;4BAAA6D,QAAA,EAAK0L,OAAO,CAACO,MAAM,CAAClJ,OAAO,CAAC,CAAC;0BAAC;4BAAA5C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpCnE,OAAA;4BAAA6D,QAAA,EAAK0L,OAAO,CAACQ;0BAAY;4BAAA/L,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GAPxBoL,OAAO,CAACC,UAAU;0BAAAxL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAQvB,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENnE,OAAA;oBAAKoM,SAAS,EAAC,oBAAoB;oBAAAvI,QAAA,EAAC;kBAAmC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC7E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNnE,OAAA;kBAAKoM,SAAS,EAAC,uBAAuB;kBAAAvI,QAAA,gBACpC7D,OAAA;oBAAIoM,SAAS,EAAC,cAAc;oBAAAvI,QAAA,gBAC1B7D,OAAA;sBAAMoM,SAAS,EAAC,OAAO;sBAAAvI,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,+BACN,EAAChD,OAAO,CAAC6O,MAAM,GAAG7O,OAAO,CAAC6O,MAAM,CAAC1I,MAAM,GAAG,CAAC;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,EACJhD,OAAO,CAAC6O,MAAM,IAAI7O,OAAO,CAAC6O,MAAM,CAAC1I,MAAM,GAAG,CAAC,gBAC1CtH,OAAA,CAAAE,SAAA;oBAAA2D,QAAA,gBACE7D,OAAA;sBAAKoM,SAAS,EAAC,uBAAuB;sBAAAvI,QAAA,eACpC7D,OAAA;wBAAOoM,SAAS,EAAC,oCAAoC;wBAAAvI,QAAA,gBACnD7D,OAAA;0BAAA6D,QAAA,eACE7D,OAAA;4BAAA6D,QAAA,gBACE7D,OAAA;8BAAA6D,QAAA,EAAI;4BAAE;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACXnE,OAAA;8BAAA6D,QAAA,EAAI;4BAAI;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACbnE,OAAA;8BAAA6D,QAAA,EAAI;4BAAU;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACnBnE,OAAA;8BAAA6D,QAAA,EAAI;4BAAU;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACRnE,OAAA;0BAAA6D,QAAA,EACG1C,OAAO,CAAC6O,MAAM,CAACvB,GAAG,CAAEwB,KAAK,iBACxBjQ,OAAA;4BAAA6D,QAAA,gBACE7D,OAAA;8BAAA6D,QAAA,EAAKoM,KAAK,CAACC;4BAAQ;8BAAAlM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACzBnE,OAAA;8BAAA6D,QAAA,EAAKoM,KAAK,CAACE;4BAAU;8BAAAnM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eAC3BnE,OAAA;8BAAA6D,QAAA,EAAKoM,KAAK,CAACR,QAAQ,CAAC7I,OAAO,CAAC,CAAC;4BAAC;8BAAA5C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACpCnE,OAAA;8BAAA6D,QAAA,EAAKoM,KAAK,CAACG;4BAAU;8BAAApM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA,GAJpB8L,KAAK,CAACC,QAAQ;4BAAAlM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAKnB,CACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,EAELhD,OAAO,CAACkP,WAAW,iBAClBrQ,OAAA;sBAAA6D,QAAA,gBACE7D,OAAA;wBAAA6D,QAAA,EAAI;sBAAmB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC5BnE,OAAA;wBAAIoM,SAAS,EAAC,kBAAkB;wBAAAvI,QAAA,EAC7B4E,MAAM,CAAC+F,OAAO,CAACrN,OAAO,CAACkP,WAAW,CAAC,CAAC5B,GAAG,CAAC,CAAC,CAACZ,IAAI,EAAEyC,KAAK,CAAC,KACrDA,KAAK,GAAG,CAAC,iBACPtQ,OAAA;0BAAA6D,QAAA,gBACE7D,OAAA;4BAAA6D,QAAA,GAASgK,IAAI,EAAC,GAAC;0BAAA;4BAAA7J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACmM,KAAK;wBAAA,GADxBzC,IAAI;0BAAA7J,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAET,CAEP;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CACN;kBAAA,eACD,CAAC,gBAEHnE,OAAA;oBAAKoM,SAAS,EAAC,oBAAoB;oBAAAvI,QAAA,EAAC;kBAAiC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC3E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNnE,OAAA;kBAAKoM,SAAS,EAAC,sBAAsB;kBAAAvI,QAAA,gBACnC7D,OAAA;oBAAIoM,SAAS,EAAC,cAAc;oBAAAvI,QAAA,gBAC1B7D,OAAA;sBAAMoM,SAAS,EAAC,OAAO;sBAAAvI,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,oBACjB,EAAChD,OAAO,CAACoP,KAAK,GAAGpP,OAAO,CAACoP,KAAK,CAACjJ,MAAM,GAAG,CAAC;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,EACJhD,OAAO,CAACoP,KAAK,IAAIpP,OAAO,CAACoP,KAAK,CAACjJ,MAAM,GAAG,CAAC,gBACxCtH,OAAA,CAAAE,SAAA;oBAAA2D,QAAA,gBACE7D,OAAA;sBAAKoM,SAAS,EAAC,uBAAuB;sBAAAvI,QAAA,eACpC7D,OAAA;wBAAOoM,SAAS,EAAC,oCAAoC;wBAAAvI,QAAA,gBACnD7D,OAAA;0BAAA6D,QAAA,eACE7D,OAAA;4BAAA6D,QAAA,gBACE7D,OAAA;8BAAA6D,QAAA,EAAI;4BAAE;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACXnE,OAAA;8BAAA6D,QAAA,EAAI;4BAAI;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACbnE,OAAA;8BAAA6D,QAAA,EAAI;4BAAS;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAClBnE,OAAA;8BAAA6D,QAAA,EAAI;4BAAM;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC,eACRnE,OAAA;0BAAA6D,QAAA,EACG1C,OAAO,CAACoP,KAAK,CAAC9B,GAAG,CAAE+B,IAAI,iBACtBxQ,OAAA;4BAAA6D,QAAA,gBACE7D,OAAA;8BAAA6D,QAAA,EAAK2M,IAAI,CAACC;4BAAO;8BAAAzM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACvBnE,OAAA;8BAAA6D,QAAA,EAAK2M,IAAI,CAACE;4BAAS;8BAAA1M,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACzBnE,OAAA;8BAAA6D,QAAA,EAAK2M,IAAI,CAACG;4BAAS;8BAAA3M,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC,eACzBnE,OAAA;8BAAA6D,QAAA,EAAK2M,IAAI,CAACI,QAAQ,CAAChK,OAAO,CAAC,CAAC;4BAAC;8BAAA5C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA,GAJ5BqM,IAAI,CAACC,OAAO;4BAAAzM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAKjB,CACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,EAELhD,OAAO,CAAC0P,gBAAgB,iBACvB7Q,OAAA;sBAAA6D,QAAA,gBACE7D,OAAA;wBAAA6D,QAAA,EAAI;sBAAuB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChCnE,OAAA;wBAAIoM,SAAS,EAAC,iBAAiB;wBAAAvI,QAAA,EAC5B4E,MAAM,CAAC+F,OAAO,CAACrN,OAAO,CAAC0P,gBAAgB,CAAC,CAACpC,GAAG,CAAC,CAAC,CAACkC,SAAS,EAAEL,KAAK,CAAC,KAC/DA,KAAK,GAAG,CAAC,iBACPtQ,OAAA;0BAAA6D,QAAA,gBACE7D,OAAA;4BAAA6D,QAAA,GAAS8M,SAAS,EAAC,GAAC;0BAAA;4BAAA3M,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,KAAC,EAACmM,KAAK;wBAAA,GAD7BK,SAAS;0BAAA3M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEd,CAEP;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CACN;kBAAA,eACD,CAAC,gBAEHnE,OAAA;oBAAKoM,SAAS,EAAC,oBAAoB;oBAAAvI,QAAA,EAAC;kBAAgC;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC1E;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNnE,OAAA;kBAAKoM,SAAS,EAAC,eAAe;kBAAAvI,QAAA,gBAC5B7D,OAAA;oBAAIoM,SAAS,EAAC,MAAM;oBAAAvI,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9CnE,OAAA;oBAAKoM,SAAS,EAAC,KAAK;oBAAAvI,QAAA,gBAClB7D,OAAA;sBAAKoM,SAAS,EAAC,oBAAoB;sBAAAvI,QAAA,gBACjC7D,OAAA;wBAAKoM,SAAS,EAAC,wBAAwB;wBAAAvI,QAAA,EAAE1C,OAAO,CAACmO,QAAQ,GAAGnO,OAAO,CAACmO,QAAQ,CAAChI,MAAM,GAAG;sBAAC;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9FnE,OAAA;wBAAKoM,SAAS,EAAC,YAAY;wBAAAvI,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNnE,OAAA;sBAAKoM,SAAS,EAAC,oBAAoB;sBAAAvI,QAAA,gBACjC7D,OAAA;wBAAKoM,SAAS,EAAC,yBAAyB;wBAAAvI,QAAA,EAAE1C,OAAO,CAAC6O,MAAM,GAAG7O,OAAO,CAAC6O,MAAM,CAAC1I,MAAM,GAAG;sBAAC;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3FnE,OAAA;wBAAKoM,SAAS,EAAC,YAAY;wBAAAvI,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC,eACNnE,OAAA;sBAAKoM,SAAS,EAAC,oBAAoB;sBAAAvI,QAAA,gBACjC7D,OAAA;wBAAKoM,SAAS,EAAC,yBAAyB;wBAAAvI,QAAA,EAAE1C,OAAO,CAACoP,KAAK,GAAGpP,OAAO,CAACoP,KAAK,CAACjJ,MAAM,GAAG;sBAAC;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzFnE,OAAA;wBAAKoM,SAAS,EAAC,YAAY;wBAAAvI,QAAA,EAAC;sBAAK;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnE,OAAA;oBAAKoM,SAAS,EAAC,kBAAkB;oBAAAvI,QAAA,eAC/B7D,OAAA;sBAAMoM,SAAS,EAAC,qBAAqB;sBAAAvI,QAAA,GAAC,iBACrB,EAAC,CAAC1C,OAAO,CAACmO,QAAQ,GAAGnO,OAAO,CAACmO,QAAQ,CAAChI,MAAM,GAAG,CAAC,KAAKnG,OAAO,CAAC6O,MAAM,GAAG7O,OAAO,CAAC6O,MAAM,CAAC1I,MAAM,GAAG,CAAC,CAAC,IAAInG,OAAO,CAACoP,KAAK,GAAGpP,OAAO,CAACoP,KAAK,CAACjJ,MAAM,GAAG,CAAC,CAAC;oBAAA;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEA5D,aAAa,KAAK,UAAU,IAAIY,OAAO,CAACmO,QAAQ,iBAC/CtP,OAAA;gBAAA6D,QAAA,gBACE7D,OAAA;kBAAA6D,QAAA,GAAI,qBAAmB,EAAC1C,OAAO,CAACmO,QAAQ,CAAChI,MAAM;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EACpDhD,OAAO,CAACmO,QAAQ,CAAChI,MAAM,GAAG,CAAC,iBAC1BtH,OAAA;kBAAKoM,SAAS,EAAC,uBAAuB;kBAAAvI,QAAA,eACpC7D,OAAA;oBAAOoM,SAAS,EAAC,oCAAoC;oBAAAvI,QAAA,gBACnD7D,OAAA;sBAAA6D,QAAA,eACE7D,OAAA;wBAAA6D,QAAA,gBACE7D,OAAA;0BAAA6D,QAAA,EAAI;wBAAE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACXnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACnBnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACnBnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAW;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACpBnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAc;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACvBnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACrBnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACRnE,OAAA;sBAAA6D,QAAA,EACG1C,OAAO,CAACmO,QAAQ,CAACb,GAAG,CAAEc,OAAO,iBAC5BvP,OAAA;wBAAA6D,QAAA,gBACE7D,OAAA;0BAAA6D,QAAA,EAAK0L,OAAO,CAACC;wBAAU;0BAAAxL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC7BnE,OAAA;0BAAA6D,QAAA,EAAK0L,OAAO,CAACE,QAAQ,CAAC7I,OAAO,CAAC,CAAC;wBAAC;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACtCnE,OAAA;0BAAA6D,QAAA,EAAK0L,OAAO,CAACG,QAAQ,GAAGH,OAAO,CAACG,QAAQ,CAAC9I,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACjEnE,OAAA;0BAAA6D,QAAA,EAAK0L,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACI,SAAS,CAAC/I,OAAO,CAAC,CAAC,CAAC,GAAG;wBAAK;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACnEnE,OAAA;0BAAA6D,QAAA,EAAK0L,OAAO,CAACK,YAAY,GAAGL,OAAO,CAACK,YAAY,CAAChJ,OAAO,CAAC,CAAC,CAAC,GAAG2I,OAAO,CAACM,QAAQ,CAACjJ,OAAO,CAAC,CAAC;wBAAC;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC/FnE,OAAA;0BAAA6D,QAAA,EAAK0L,OAAO,CAACO,MAAM,CAAClJ,OAAO,CAAC,CAAC;wBAAC;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACpCnE,OAAA;0BAAA6D,QAAA,EAAK0L,OAAO,CAACQ;wBAAY;0BAAA/L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,GAPxBoL,OAAO,CAACC,UAAU;wBAAAxL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAQvB,CACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EAEA5D,aAAa,KAAK,QAAQ,IAAIY,OAAO,CAAC6O,MAAM,iBAC3ChQ,OAAA;gBAAA6D,QAAA,gBACE7D,OAAA;kBAAA6D,QAAA,GAAI,mBAAiB,EAAC1C,OAAO,CAAC6O,MAAM,CAAC1I,MAAM;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAChDhD,OAAO,CAAC6O,MAAM,CAAC1I,MAAM,GAAG,CAAC,iBACxBtH,OAAA;kBAAKoM,SAAS,EAAC,uBAAuB;kBAAAvI,QAAA,eACpC7D,OAAA;oBAAOoM,SAAS,EAAC,oCAAoC;oBAAAvI,QAAA,gBACnD7D,OAAA;sBAAA6D,QAAA,eACE7D,OAAA;wBAAA6D,QAAA,gBACE7D,OAAA;0BAAA6D,QAAA,EAAI;wBAAE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACXnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAI;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACbnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACnBnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACRnE,OAAA;sBAAA6D,QAAA,EACG1C,OAAO,CAAC6O,MAAM,CAACvB,GAAG,CAAEwB,KAAK,iBACxBjQ,OAAA;wBAAA6D,QAAA,gBACE7D,OAAA;0BAAA6D,QAAA,EAAKoM,KAAK,CAACC;wBAAQ;0BAAAlM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACzBnE,OAAA;0BAAA6D,QAAA,EAAKoM,KAAK,CAACE;wBAAU;0BAAAnM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eAC3BnE,OAAA;0BAAA6D,QAAA,EAAKoM,KAAK,CAACR,QAAQ,CAAC7I,OAAO,CAAC,CAAC;wBAAC;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACpCnE,OAAA;0BAAA6D,QAAA,EAAKoM,KAAK,CAACG;wBAAU;0BAAApM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,GAJpB8L,KAAK,CAACC,QAAQ;wBAAAlM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAKnB,CACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACN,EAEAhD,OAAO,CAACkP,WAAW,iBAClBrQ,OAAA;kBAAA6D,QAAA,gBACE7D,OAAA;oBAAA6D,QAAA,EAAI;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5BnE,OAAA;oBAAIoM,SAAS,EAAC,kBAAkB;oBAAAvI,QAAA,EAC7B4E,MAAM,CAAC+F,OAAO,CAACrN,OAAO,CAACkP,WAAW,CAAC,CAAC5B,GAAG,CAAC,CAAC,CAACZ,IAAI,EAAEyC,KAAK,CAAC,KACrDA,KAAK,GAAG,CAAC,iBACPtQ,OAAA;sBAAA6D,QAAA,gBACE7D,OAAA;wBAAA6D,QAAA,GAASgK,IAAI,EAAC,GAAC;sBAAA;wBAAA7J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACmM,KAAK;oBAAA,GADxBzC,IAAI;sBAAA7J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAET,CAEP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EAEA5D,aAAa,KAAK,OAAO,IAAIY,OAAO,CAACoP,KAAK,iBACzCvQ,OAAA;gBAAA6D,QAAA,gBACE7D,OAAA;kBAAA6D,QAAA,GAAI,kBAAgB,EAAC1C,OAAO,CAACoP,KAAK,CAACjJ,MAAM;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC9ChD,OAAO,CAACoP,KAAK,CAACjJ,MAAM,GAAG,CAAC,iBACvBtH,OAAA;kBAAKoM,SAAS,EAAC,uBAAuB;kBAAAvI,QAAA,eACpC7D,OAAA;oBAAOoM,SAAS,EAAC,oCAAoC;oBAAAvI,QAAA,gBACnD7D,OAAA;sBAAA6D,QAAA,eACE7D,OAAA;wBAAA6D,QAAA,gBACE7D,OAAA;0BAAA6D,QAAA,EAAI;wBAAE;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACXnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAI;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACbnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAClBnE,OAAA;0BAAA6D,QAAA,EAAI;wBAAM;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACRnE,OAAA;sBAAA6D,QAAA,EACG1C,OAAO,CAACoP,KAAK,CAAC9B,GAAG,CAAE+B,IAAI,iBACtBxQ,OAAA;wBAAA6D,QAAA,gBACE7D,OAAA;0BAAA6D,QAAA,EAAK2M,IAAI,CAACC;wBAAO;0BAAAzM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACvBnE,OAAA;0BAAA6D,QAAA,EAAK2M,IAAI,CAACE;wBAAS;0BAAA1M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACzBnE,OAAA;0BAAA6D,QAAA,EAAK2M,IAAI,CAACG;wBAAS;0BAAA3M,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACzBnE,OAAA;0BAAA6D,QAAA,EAAK2M,IAAI,CAACI,QAAQ,CAAChK,OAAO,CAAC,CAAC;wBAAC;0BAAA5C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA,GAJ5BqM,IAAI,CAACC,OAAO;wBAAAzM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAKjB,CACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACN,EAEAhD,OAAO,CAAC0P,gBAAgB,iBACvB7Q,OAAA;kBAAA6D,QAAA,gBACE7D,OAAA;oBAAA6D,QAAA,EAAI;kBAAuB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChCnE,OAAA;oBAAIoM,SAAS,EAAC,sBAAsB;oBAAAvI,QAAA,EACjC4E,MAAM,CAAC+F,OAAO,CAACrN,OAAO,CAAC0P,gBAAgB,CAAC,CAACpC,GAAG,CAAC,CAAC,CAACkC,SAAS,EAAEL,KAAK,CAAC,KAC/DA,KAAK,GAAG,CAAC,iBACPtQ,OAAA;sBAAA6D,QAAA,gBACE7D,OAAA;wBAAA6D,QAAA,GAAS8M,SAAS,EAAC,GAAC;sBAAA;wBAAA3M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACmM,KAAK;oBAAA,GAD7BK,SAAS;sBAAA3M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEd,CAEP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACP,EAGA5B,eAAe,iBACdvC,OAAA;UAAKoM,SAAS,EAAC,8BAA8B;UAAAvI,QAAA,eAC3C7D,OAAA;YAAKoM,SAAS,EAAC,2BAA2B;YAAAvI,QAAA,gBACxC7D,OAAA;cAAKoM,SAAS,EAAC,MAAM;cAAAvI,QAAA,eACnB7D,OAAA,CAACT,OAAO;gBAACoO,SAAS,EAAC,QAAQ;gBAACC,IAAI,EAAC,IAAI;gBAAC/D,IAAI,EAAC;cAAQ;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNnE,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAIoM,SAAS,EAAC,MAAM;gBAAAvI,QAAA,GAAC,qBAAmB,EAACpB,cAAc,EAAC,GAAC,EAACM,cAAc;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9EnE,OAAA;gBAAKoM,SAAS,EAAC,UAAU;gBAACzI,KAAK,EAAE;kBAAE4J,MAAM,EAAE;gBAAO,CAAE;gBAAA1J,QAAA,eAClD7D,OAAA;kBACEoM,SAAS,EAAC,cAAc;kBACxBvC,IAAI,EAAC,aAAa;kBAClBlG,KAAK,EAAE;oBAAE2J,KAAK,EAAE,GAAI7K,cAAc,GAAGM,cAAc,GAAI,GAAG;kBAAI,CAAE;kBAChE,iBAAeN,cAAe;kBAC9B,iBAAc,GAAG;kBACjB,iBAAeM;gBAAe;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA,CAAC5B,eAAe,IAAIF,YAAY,CAACiF,MAAM,GAAG,CAAC,iBAC1CtH,OAAA;UAAKoM,SAAS,EAAC,4BAA4B;UAAAvI,QAAA,eACzC7D,OAAA,CAACV,KAAK;YAACkO,OAAO,EAAC,SAAS;YAAA3J,QAAA,gBACtB7D,OAAA;cAAGoM,SAAS,EAAC;YAA0B;cAAApI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,cAClC,EAAC9B,YAAY,CAACiF,MAAM,EAAC,UAC/B,EAACjF,YAAY,CAAC8I,MAAM,CAAC2F,CAAC,IAAIA,CAAC,CAAC5G,OAAO,CAAC,CAAC5C,MAAM,EAAC,cAC5C,EAACjF,YAAY,CAAC8I,MAAM,CAAC2F,CAAC,IAAI,CAACA,CAAC,CAAC5G,OAAO,IAAI,CAAC4G,CAAC,CAACnG,qBAAqB,CAAC,CAACrD,MAAM,EAAC,UACzE,EAACjF,YAAY,CAAC8I,MAAM,CAAC2F,CAAC,IAAIA,CAAC,CAACnG,qBAAqB,CAAC,CAACrD,MAAM,GAAG,CAAC,iBAC3DtH,OAAA;cAAKoM,SAAS,EAAC,MAAM;cAAAvI,QAAA,eACnB7D,OAAA;gBAAOoM,SAAS,EAAC,cAAc;gBAAAvI,QAAA,gBAC7B7D,OAAA;kBAAGoM,SAAS,EAAC;gBAAkC;kBAAApI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACnD9B,YAAY,CAAC8I,MAAM,CAAC2F,CAAC,IAAIA,CAAC,CAACnG,qBAAqB,CAAC,CAACrD,MAAM,EAAC,8BAC5D;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAGA9B,YAAY,CAACiF,MAAM,GAAG,CAAC,IAAI,CAAC/E,eAAe,iBAC1CvC,OAAA;UAAKoM,SAAS,EAAC,sDAAsD;UAAAvI,QAAA,gBACnE7D,OAAA,CAACd,MAAM;YACLsO,OAAO,EAAC,mBAAmB;YAC3BI,IAAI,EAAC,IAAI;YACTF,QAAQ,EAAE3M,iBAAiB,KAAK,CAAE;YAClC0M,OAAO,EAAEA,CAAA,KAAM;cACb,IAAI1M,iBAAiB,GAAG,CAAC,EAAE;gBACzB,MAAMgQ,QAAQ,GAAGhQ,iBAAiB,GAAG,CAAC;gBACtCC,oBAAoB,CAAC+P,QAAQ,CAAC;;gBAE9B;gBACA,MAAMzI,QAAQ,GAAGG,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAACoQ,QAAQ,CAAC;gBACxD,MAAMlJ,MAAM,GAAGxF,YAAY,CAAC2O,IAAI,CAACF,CAAC,IAAIA,CAAC,CAACxI,QAAQ,KAAKA,QAAQ,CAAC;gBAE9D,IAAIT,MAAM,IAAIA,MAAM,CAACqC,OAAO,EAAE;kBAC5BhJ,iBAAiB,CAAC2G,MAAM,CAAC5G,cAAc,CAAC;kBACxCG,UAAU,CAACyG,MAAM,CAACoC,IAAI,CAAC;gBACzB,CAAC,MAAM;kBACL/I,iBAAiB,CAAC,IAAI,CAAC;kBACvBE,UAAU,CAAC,IAAI,CAAC;gBAClB;cACF;YACF,CAAE;YAAAyC,QAAA,gBAEF7D,OAAA;cAAGoM,SAAS,EAAC;YAAwB;cAAApI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mBAC5C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETnE,OAAA;YAAKoM,SAAS,EAAC,eAAe;YAAAvI,QAAA,GAAC,QACvB,EAAC9C,iBAAiB,GAAG,CAAC,EAAC,MAAI,EAAC0H,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM;UAAA;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAENnE,OAAA,CAACd,MAAM;YACLsO,OAAO,EAAC,mBAAmB;YAC3BI,IAAI,EAAC,IAAI;YACTF,QAAQ,EAAE3M,iBAAiB,IAAI0H,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,GAAG,CAAE;YACxEmG,OAAO,EAAEA,CAAA,KAAM;cACb,IAAI1M,iBAAiB,GAAG0H,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAAC2G,MAAM,GAAG,CAAC,EAAE;gBAChE,MAAMyJ,QAAQ,GAAGhQ,iBAAiB,GAAG,CAAC;gBACtCC,oBAAoB,CAAC+P,QAAQ,CAAC;;gBAE9B;gBACA,MAAMzI,QAAQ,GAAGG,MAAM,CAACC,IAAI,CAAC/H,gBAAgB,CAAC,CAACoQ,QAAQ,CAAC;gBACxD,MAAMlJ,MAAM,GAAGxF,YAAY,CAAC2O,IAAI,CAACF,CAAC,IAAIA,CAAC,CAACxI,QAAQ,KAAKA,QAAQ,CAAC;gBAE9D,IAAIT,MAAM,IAAIA,MAAM,CAACqC,OAAO,EAAE;kBAC5BhJ,iBAAiB,CAAC2G,MAAM,CAAC5G,cAAc,CAAC;kBACxCG,UAAU,CAACyG,MAAM,CAACoC,IAAI,CAAC;gBACzB,CAAC,MAAM;kBACL/I,iBAAiB,CAAC,IAAI,CAAC;kBACvBE,UAAU,CAAC,IAAI,CAAC;gBAClB;cACF;YACF,CAAE;YAAAyC,QAAA,GACH,aACY,eAAA7D,OAAA;cAAGoM,SAAS,EAAC;YAAyB;cAAApI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnE,OAAA,CAACX,GAAG;QAACmN,QAAQ,EAAC,OAAO;QAACC,KAAK,EAAC,iBAAiB;QAAA5I,QAAA,eAC3C7D,OAAA,CAACF,oBAAoB;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eAENnE,OAAA,CAACX,GAAG;QAACmN,QAAQ,EAAC,aAAa;QAACC,KAAK,EAAC,aAAa;QAAA5I,QAAA,eAC7C7D,OAAA,CAACf,IAAI;UAAA4E,QAAA,eACH7D,OAAA,CAACf,IAAI,CAACmF,IAAI;YAAAP,QAAA,gBACR7D,OAAA;cAAA6D,QAAA,EAAI;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChCnE,OAAA;cAAA6D,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJnE,OAAA;cAAA6D,QAAA,EAAI;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBnE,OAAA;cAAA6D,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnE,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAA6D,QAAA,EAAI;cAA0B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCnE,OAAA;gBAAA6D,QAAA,EAAI;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7BnE,OAAA;gBAAA6D,QAAA,EAAI;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfnE,OAAA;gBAAA6D,QAAA,EAAI;cAA6C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAELnE,OAAA;cAAA6D,QAAA,EAAI;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BnE,OAAA;cAAA6D,QAAA,EAAG;YAIH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnE,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAA6D,QAAA,EAAI;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBnE,OAAA;gBAAA6D,QAAA,EAAI;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBnE,OAAA;gBAAA6D,QAAA,EAAI;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBnE,OAAA;gBAAA6D,QAAA,EAAI;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BnE,OAAA;gBAAA6D,QAAA,EAAI;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eAELnE,OAAA;cAAA6D,QAAA,EAAI;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBnE,OAAA;cAAA6D,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnE,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAA6D,QAAA,EAAI;cAA2D;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEnE,OAAA;gBAAA6D,QAAA,EAAI;cAAgE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzEnE,OAAA;gBAAA6D,QAAA,EAAI;cAAmE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eAELnE,OAAA;cAAA6D,QAAA,EAAI;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrCnE,OAAA;cAAA6D,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnE,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAA6D,QAAA,EAAI;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5CnE,OAAA;gBAAA6D,QAAA,EAAI;cAA+B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCnE,OAAA;gBAAA6D,QAAA,EAAI;cAA4C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDnE,OAAA;gBAAA6D,QAAA,EAAI;cAA2C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eAELnE,OAAA;cAAA6D,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BnE,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAA6D,QAAA,gBAAI7D,OAAA;kBAAA6D,QAAA,EAAQ;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,oCAAgC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5EnE,OAAA;gBAAA6D,QAAA,gBAAI7D,OAAA;kBAAA6D,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,iDAA6C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3FnE,OAAA;gBAAA6D,QAAA,gBAAI7D,OAAA;kBAAA6D,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gDAA4C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFnE,OAAA;gBAAA6D,QAAA,gBAAI7D,OAAA;kBAAA6D,QAAA,EAAQ;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,4DAAwD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eAELnE,OAAA;cAAKoM,SAAS,EAAC,kBAAkB;cAAAvI,QAAA,gBAC/B7D,OAAA;gBAAA6D,QAAA,gBAAI7D,OAAA;kBAAGoM,SAAS,EAAC;gBAAyB;kBAAApI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,mCAA+B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFnE,OAAA;gBAAA6D,QAAA,eAAG7D,OAAA;kBAAA6D,QAAA,EAAQ;gBAA6B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrDnE,OAAA;gBAAIoM,SAAS,EAAC,MAAM;gBAAAvI,QAAA,gBAClB7D,OAAA;kBAAA6D,QAAA,gBAAI7D,OAAA;oBAAA6D,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gEAAkD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFnE,OAAA;kBAAA6D,QAAA,gBAAI7D,OAAA;oBAAA6D,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,8EAA2D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5FnE,OAAA;kBAAA6D,QAAA,gBAAI7D,OAAA;oBAAA6D,QAAA,EAAQ;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,0EAAuD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACLnE,OAAA;gBAAA6D,QAAA,gBAAG7D,OAAA;kBAAA6D,QAAA,EAAQ;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,+FAA2F;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClI,CAAC,eAENnE,OAAA;cAAA6D,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BnE,OAAA;cAAA6D,QAAA,gBACE7D,OAAA;gBAAA6D,QAAA,EAAI;cAAgE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzEnE,OAAA;gBAAA6D,QAAA,EAAI;cAAoD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7DnE,OAAA;gBAAA6D,QAAA,EAAI;cAA4E;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrFnE,OAAA;gBAAA6D,QAAA,EAAI;cAA4C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDnE,OAAA;gBAAA6D,QAAA,EAAI;cAA6C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAELnE,OAAA;cAAA6D,QAAA,EAAG;YAGH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPnE,OAAA,CAACN,KAAK;MACJuR,IAAI,EAAEtO,uBAAwB;MAC9BuO,MAAM,EAAEA,CAAA,KAAMtO,0BAA0B,CAAC,KAAK,CAAE;MAChDuO,QAAQ;MAAAtN,QAAA,gBAER7D,OAAA,CAACN,KAAK,CAACoE,MAAM;QAACsN,WAAW;QAAAvN,QAAA,eACvB7D,OAAA,CAACN,KAAK,CAAC2R,KAAK;UAAAxN,QAAA,gBACV7D,OAAA;YAAGoM,SAAS,EAAC;UAA+C;YAAApI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,yBAEnE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfnE,OAAA,CAACN,KAAK,CAAC0E,IAAI;QAAAP,QAAA,eACT7D,OAAA;UAAKoM,SAAS,EAAC,aAAa;UAAAvI,QAAA,gBAC1B7D,OAAA;YAAKoM,SAAS,EAAC,MAAM;YAAAvI,QAAA,eACnB7D,OAAA;cAAGoM,SAAS,EAAC;YAA8B;cAAApI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNnE,OAAA;YAAIoM,SAAS,EAAC,kBAAkB;YAAAvI,QAAA,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDnE,OAAA;YAAGoM,SAAS,EAAC,MAAM;YAAAvI,QAAA,EAChBhB,mBAAmB,IAAI;UAAsI;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7J,CAAC,eACJnE,OAAA;YAAKoM,SAAS,EAAC,kBAAkB;YAAAvI,QAAA,gBAC/B7D,OAAA;cAAA6D,QAAA,EAAQ;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACzCnE,OAAA;cAAIoM,SAAS,EAAC,sBAAsB;cAAAvI,QAAA,gBAClC7D,OAAA;gBAAA6D,QAAA,EAAI;cAA6C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDnE,OAAA;gBAAA6D,QAAA,EAAI;cAAoD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7DnE,OAAA;gBAAA6D,QAAA,EAAI;cAA8D;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEnE,OAAA;gBAAA6D,QAAA,EAAI;cAAwD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACbnE,OAAA,CAACN,KAAK,CAAC4R,MAAM;QAAAzN,QAAA,eACX7D,OAAA,CAACd,MAAM;UACLsO,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEA,CAAA,KAAM7K,0BAA0B,CAAC,KAAK,CAAE;UAAAiB,QAAA,EAClD;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAAC/D,EAAA,CAz6CID,QAAQ;EAAA,QAmCSN,aAAa;AAAA;AAAA0R,EAAA,GAnC9BpR,QAAQ;AA26Cd,eAAeA,QAAQ;AAAC,IAAAoR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}