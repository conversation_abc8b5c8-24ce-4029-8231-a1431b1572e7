{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTA_GIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\DefectDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { Container, Row, Col, Card, But<PERSON>, Spinner, <PERSON>ert, Badge } from 'react-bootstrap';\nimport axios from 'axios';\nimport './dashboard.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction DefectDetail() {\n  _s();\n  const {\n    imageId\n  } = useParams();\n  const [defectData, setDefectData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [imageType, setImageType] = useState('processed'); // 'original' or 'processed'\n\n  useEffect(() => {\n    const fetchDefectDetail = async () => {\n      try {\n        setLoading(true);\n        const response = await axios.get(`/api/pavement/images/${imageId}`);\n        if (response.data.success) {\n          setDefectData(response.data);\n        } else {\n          setError('Failed to load defect details');\n        }\n        setLoading(false);\n      } catch (err) {\n        console.error('Error fetching defect details:', err);\n        setError(`Error loading defect details: ${err.message}`);\n        setLoading(false);\n      }\n    };\n    if (imageId) {\n      fetchDefectDetail();\n    }\n  }, [imageId]);\n  const toggleImageType = () => {\n    setImageType(prev => prev === 'original' ? 'processed' : 'original');\n  };\n  const getDefectTypeLabel = type => {\n    switch (type) {\n      case 'pothole':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"danger\",\n          children: \"Pothole\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 16\n        }, this);\n      case 'crack':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"warning\",\n          text: \"dark\",\n          children: \"Crack\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 16\n        }, this);\n      case 'kerb':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"primary\",\n          children: \"Kerb\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"secondary\",\n          children: type\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleString();\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"py-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Defect Detail\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/dashboard\",\n        className: \"btn btn-outline-primary\",\n        children: \"Back to Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-5\",\n      children: /*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        role: \"status\",\n        variant: \"primary\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 9\n    }, this) : defectData ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        className: \"shadow-sm mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"bg-primary text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-between align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [getDefectTypeLabel(defectData.type), \" - ID: \", imageId]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: imageType === 'original' ? 'light' : 'outline-light',\n                size: \"sm\",\n                className: \"me-2\",\n                onClick: toggleImageType,\n                children: \"Original\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: imageType === 'processed' ? 'light' : 'outline-light',\n                size: \"sm\",\n                onClick: toggleImageType,\n                children: \"Processed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              className: \"text-center mb-4\",\n              children: defectData.image && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"defect-image-container\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `/api/pavement/get-image/${imageType === 'original' ? defectData.image.original_image_id : defectData.image.processed_image_id}`,\n                  alt: `${defectData.type} defect`,\n                  className: \"img-fluid border rounded shadow-sm\",\n                  style: {\n                    maxHeight: '400px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                children: \"Basic Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-bordered\",\n                children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      width: \"40%\",\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 127,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 128,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Defect Count\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 131,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.image.pothole_count || defectData.image.crack_count || defectData.image.kerb_count || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 132,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Date Detected\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 139,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: formatDate(defectData.image.timestamp)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Reported By\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 143,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.image.username || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Role\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.image.role || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Coordinates\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      children: defectData.image.coordinates || 'Not Available'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 152,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this), defectData.type === 'pothole' && defectData.image.potholes && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Pothole Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-striped table-bordered\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"table-primary\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Area (cm\\xB2)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 167,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Width (cm)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Length (cm)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Max Depth (cm)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Volume (cm\\xB3)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Severity\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 172,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: defectData.image.potholes.map((pothole, index) => {\n                    var _pothole$area_cm, _pothole$width_cm, _pothole$length_cm, _pothole$max_depth_cm, _pothole$depth_cm, _pothole$volume;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: pothole.pothole_id\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 178,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_pothole$area_cm = pothole.area_cm2) === null || _pothole$area_cm === void 0 ? void 0 : _pothole$area_cm.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 179,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_pothole$width_cm = pothole.width_cm) === null || _pothole$width_cm === void 0 ? void 0 : _pothole$width_cm.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 180,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_pothole$length_cm = pothole.length_cm) === null || _pothole$length_cm === void 0 ? void 0 : _pothole$length_cm.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 181,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_pothole$max_depth_cm = pothole.max_depth_cm) === null || _pothole$max_depth_cm === void 0 ? void 0 : _pothole$max_depth_cm.toFixed(2)) || ((_pothole$depth_cm = pothole.depth_cm) === null || _pothole$depth_cm === void 0 ? void 0 : _pothole$depth_cm.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_pothole$volume = pothole.volume) === null || _pothole$volume === void 0 ? void 0 : _pothole$volume.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 183,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: pothole.area_cm2 > 1000 ? /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"danger\",\n                          children: \"High\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 186,\n                          columnNumber: 33\n                        }, this) : pothole.area_cm2 > 500 ? /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"warning\",\n                          text: \"dark\",\n                          children: \"Medium\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 188,\n                          columnNumber: 33\n                        }, this) : /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: \"success\",\n                          children: \"Low\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 190,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 17\n          }, this), defectData.type === 'crack' && defectData.image.cracks && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Crack Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-striped table-bordered\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"table-primary\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Area (cm\\xB2)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Confidence\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: defectData.image.cracks.map((crack, index) => {\n                    var _crack$area_cm;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: crack.crack_id\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: crack.crack_type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_crack$area_cm = crack.area_cm2) === null || _crack$area_cm === void 0 ? void 0 : _crack$area_cm.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: [(crack.confidence * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 19\n            }, this), defectData.image.type_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Crack Type Distribution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-wrap\",\n                children: Object.entries(defectData.image.type_counts).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"me-3 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Badge, {\n                    bg: \"info\",\n                    className: \"me-1\",\n                    children: count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 29\n                  }, this), \" \", type]\n                }, type, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 17\n          }, this), defectData.type === 'kerb' && defectData.image.kerbs && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Kerb Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"table-responsive\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"table table-striped table-bordered\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"table-primary\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Condition\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      children: \"Length (m)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: defectData.image.kerbs.map((kerb, index) => {\n                    var _kerb$length_m;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        children: kerb.kerb_id\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: kerb.kerb_type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: /*#__PURE__*/_jsxDEV(Badge, {\n                          bg: kerb.condition === 'Good' ? 'success' : kerb.condition === 'Fair' ? 'warning' : 'danger',\n                          text: kerb.condition === 'Fair' ? 'dark' : undefined,\n                          children: kerb.condition\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 261,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        children: ((_kerb$length_m = kerb.length_m) === null || _kerb$length_m === void 0 ? void 0 : _kerb$length_m.toFixed(2)) || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 271,\n                        columnNumber: 29\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 19\n            }, this), defectData.image.condition_counts && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Condition Distribution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-wrap\",\n                children: Object.entries(defectData.image.condition_counts).map(([condition, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"me-3 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Badge, {\n                    bg: condition === 'Good' ? 'success' : condition === 'Fair' ? 'warning' : 'danger',\n                    text: condition === 'Fair' ? 'dark' : undefined,\n                    className: \"me-1\",\n                    children: count\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 29\n                  }, this), \" \", condition]\n                }, condition, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"bg-light\",\n              children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                children: /*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"mb-0\",\n                  children: \"Recommended Action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Based on the defect analysis, the following action is recommended:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this), defectData.type === 'pothole' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Clean out loose material\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Apply tack coat\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Fill with hot mix asphalt\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Compact thoroughly\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 28\n                    }, this), \" \", defectData.image.potholes && defectData.image.potholes.length > 0 && defectData.image.potholes.some(p => p.area_cm2 > 1000) ? 'High' : defectData.image.potholes && defectData.image.potholes.length > 0 && defectData.image.potholes.some(p => p.area_cm2 > 500) ? 'Medium' : 'Low']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 23\n                }, this), defectData.type === 'crack' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Clean cracks with compressed air\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Apply appropriate crack sealant\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 27\n                    }, this), defectData.image.type_counts && defectData.image.type_counts['Alligator Crack'] > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Consider section replacement for alligator crack areas\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 28\n                    }, this), \" \", defectData.image.type_counts && defectData.image.type_counts['Alligator Crack'] > 0 ? 'High' : 'Medium']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this), defectData.type === 'kerb' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Repair damaged sections\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Realign displaced kerbs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: \"Replace severely damaged kerbs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Priority:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 28\n                    }, this), \" \", defectData.image.condition_counts && defectData.image.condition_counts['Poor'] > 0 ? 'High' : defectData.image.condition_counts['Fair'] > 0 ? 'Medium' : 'Low']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          children: \"Generate Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"warning\",\n      children: [\"No defect data found for ID: \", imageId]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_s(DefectDetail, \"kQjyLq30HWoqEhsuowi/BrDiCFw=\", false, function () {\n  return [useParams];\n});\n_c = DefectDetail;\nexport default DefectDetail;\nvar _c;\n$RefreshReg$(_c, \"DefectDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Link", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Spinner", "<PERSON><PERSON>", "Badge", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DefectDetail", "_s", "imageId", "defectData", "setDefectData", "loading", "setLoading", "error", "setError", "imageType", "setImageType", "fetchDefectDetail", "response", "get", "data", "success", "err", "console", "message", "toggleImageType", "prev", "getDefectTypeLabel", "type", "bg", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "formatDate", "dateString", "Date", "toLocaleString", "className", "to", "animation", "role", "variant", "Header", "size", "onClick", "Body", "md", "image", "src", "original_image_id", "processed_image_id", "alt", "style", "maxHeight", "width", "pothole_count", "crack_count", "kerb_count", "timestamp", "username", "coordinates", "potholes", "map", "pothole", "index", "_pothole$area_cm", "_pothole$width_cm", "_pothole$length_cm", "_pothole$max_depth_cm", "_pothole$depth_cm", "_pothole$volume", "pothole_id", "area_cm2", "toFixed", "width_cm", "length_cm", "max_depth_cm", "depth_cm", "volume", "cracks", "crack", "_crack$area_cm", "crack_id", "crack_type", "confidence", "type_counts", "Object", "entries", "count", "kerbs", "kerb", "_kerb$length_m", "kerb_id", "kerb_type", "condition", "undefined", "length_m", "condition_counts", "length", "some", "p", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTA_GIT/LTA/frontend/src/pages/DefectDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\r\nimport { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON>, <PERSON>ge } from 'react-bootstrap';\r\nimport axios from 'axios';\r\nimport './dashboard.css';\r\n\r\nfunction DefectDetail() {\r\n  const { imageId } = useParams();\r\n  const [defectData, setDefectData] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [imageType, setImageType] = useState('processed'); // 'original' or 'processed'\r\n\r\n  useEffect(() => {\r\n    const fetchDefectDetail = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await axios.get(`/api/pavement/images/${imageId}`);\r\n        \r\n        if (response.data.success) {\r\n          setDefectData(response.data);\r\n        } else {\r\n          setError('Failed to load defect details');\r\n        }\r\n        setLoading(false);\r\n      } catch (err) {\r\n        console.error('Error fetching defect details:', err);\r\n        setError(`Error loading defect details: ${err.message}`);\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (imageId) {\r\n      fetchDefectDetail();\r\n    }\r\n  }, [imageId]);\r\n\r\n  const toggleImageType = () => {\r\n    setImageType(prev => prev === 'original' ? 'processed' : 'original');\r\n  };\r\n\r\n  const getDefectTypeLabel = (type) => {\r\n    switch (type) {\r\n      case 'pothole':\r\n        return <Badge bg=\"danger\">Pothole</Badge>;\r\n      case 'crack':\r\n        return <Badge bg=\"warning\" text=\"dark\">Crack</Badge>;\r\n      case 'kerb':\r\n        return <Badge bg=\"primary\">Kerb</Badge>;\r\n      default:\r\n        return <Badge bg=\"secondary\">{type}</Badge>;\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return 'N/A';\r\n    return new Date(dateString).toLocaleString();\r\n  };\r\n\r\n  return (\r\n    <Container className=\"py-4\">\r\n      <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n        <h2>Defect Detail</h2>\r\n        <Link to=\"/dashboard\" className=\"btn btn-outline-primary\">\r\n          Back to Dashboard\r\n        </Link>\r\n      </div>\r\n\r\n      {loading ? (\r\n        <div className=\"text-center py-5\">\r\n          <Spinner animation=\"border\" role=\"status\" variant=\"primary\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </Spinner>\r\n        </div>\r\n      ) : error ? (\r\n        <Alert variant=\"danger\">{error}</Alert>\r\n      ) : defectData ? (\r\n        <>\r\n          <Card className=\"shadow-sm mb-4\">\r\n            <Card.Header className=\"bg-primary text-white\">\r\n              <div className=\"d-flex justify-content-between align-items-center\">\r\n                <h5 className=\"mb-0\">\r\n                  {getDefectTypeLabel(defectData.type)} - ID: {imageId}\r\n                </h5>\r\n                <div>\r\n                  <Button \r\n                    variant={imageType === 'original' ? 'light' : 'outline-light'} \r\n                    size=\"sm\" \r\n                    className=\"me-2\"\r\n                    onClick={toggleImageType}\r\n                  >\r\n                    Original\r\n                  </Button>\r\n                  <Button \r\n                    variant={imageType === 'processed' ? 'light' : 'outline-light'} \r\n                    size=\"sm\"\r\n                    onClick={toggleImageType}\r\n                  >\r\n                    Processed\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              <Row>\r\n                <Col md={6} className=\"text-center mb-4\">\r\n                  {defectData.image && (\r\n                    <div className=\"defect-image-container\">\r\n                      <img \r\n                        src={`/api/pavement/get-image/${\r\n                          imageType === 'original' \r\n                            ? defectData.image.original_image_id \r\n                            : defectData.image.processed_image_id\r\n                        }`}\r\n                        alt={`${defectData.type} defect`}\r\n                        className=\"img-fluid border rounded shadow-sm\"\r\n                        style={{ maxHeight: '400px' }}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </Col>\r\n                <Col md={6}>\r\n                  <h5>Basic Information</h5>\r\n                  <table className=\"table table-bordered\">\r\n                    <tbody>\r\n                      <tr>\r\n                        <th width=\"40%\">Type</th>\r\n                        <td>{defectData.type}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Defect Count</th>\r\n                        <td>\r\n                          {defectData.image.pothole_count || \r\n                           defectData.image.crack_count || \r\n                           defectData.image.kerb_count || 'N/A'}\r\n                        </td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Date Detected</th>\r\n                        <td>{formatDate(defectData.image.timestamp)}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Reported By</th>\r\n                        <td>{defectData.image.username || 'N/A'}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Role</th>\r\n                        <td>{defectData.image.role || 'N/A'}</td>\r\n                      </tr>\r\n                      <tr>\r\n                        <th>Coordinates</th>\r\n                        <td>{defectData.image.coordinates || 'Not Available'}</td>\r\n                      </tr>\r\n                    </tbody>\r\n                  </table>\r\n                </Col>\r\n              </Row>\r\n\r\n              {defectData.type === 'pothole' && defectData.image.potholes && (\r\n                <div className=\"mt-4\">\r\n                  <h5>Pothole Details</h5>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped table-bordered\">\r\n                      <thead className=\"table-primary\">\r\n                        <tr>\r\n                          <th>ID</th>\r\n                          <th>Area (cm²)</th>\r\n                          <th>Width (cm)</th>\r\n                          <th>Length (cm)</th>\r\n                          <th>Max Depth (cm)</th>\r\n                          <th>Volume (cm³)</th>\r\n                          <th>Severity</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {defectData.image.potholes.map((pothole, index) => (\r\n                          <tr key={index}>\r\n                            <td>{pothole.pothole_id}</td>\r\n                            <td>{pothole.area_cm2?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{pothole.width_cm?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{pothole.length_cm?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{pothole.max_depth_cm?.toFixed(2) || pothole.depth_cm?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{pothole.volume?.toFixed(2) || 'N/A'}</td>\r\n                            <td>\r\n                              {pothole.area_cm2 > 1000 ? (\r\n                                <Badge bg=\"danger\">High</Badge>\r\n                              ) : pothole.area_cm2 > 500 ? (\r\n                                <Badge bg=\"warning\" text=\"dark\">Medium</Badge>\r\n                              ) : (\r\n                                <Badge bg=\"success\">Low</Badge>\r\n                              )}\r\n                            </td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {defectData.type === 'crack' && defectData.image.cracks && (\r\n                <div className=\"mt-4\">\r\n                  <h5>Crack Details</h5>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped table-bordered\">\r\n                      <thead className=\"table-primary\">\r\n                        <tr>\r\n                          <th>ID</th>\r\n                          <th>Type</th>\r\n                          <th>Area (cm²)</th>\r\n                          <th>Confidence</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {defectData.image.cracks.map((crack, index) => (\r\n                          <tr key={index}>\r\n                            <td>{crack.crack_id}</td>\r\n                            <td>{crack.crack_type}</td>\r\n                            <td>{crack.area_cm2?.toFixed(2) || 'N/A'}</td>\r\n                            <td>{(crack.confidence * 100).toFixed(1)}%</td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                  \r\n                  {defectData.image.type_counts && (\r\n                    <div className=\"mt-3\">\r\n                      <h6>Crack Type Distribution</h6>\r\n                      <div className=\"d-flex flex-wrap\">\r\n                        {Object.entries(defectData.image.type_counts).map(([type, count]) => (\r\n                          <div key={type} className=\"me-3 mb-2\">\r\n                            <Badge bg=\"info\" className=\"me-1\">{count}</Badge> {type}\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {defectData.type === 'kerb' && defectData.image.kerbs && (\r\n                <div className=\"mt-4\">\r\n                  <h5>Kerb Details</h5>\r\n                  <div className=\"table-responsive\">\r\n                    <table className=\"table table-striped table-bordered\">\r\n                      <thead className=\"table-primary\">\r\n                        <tr>\r\n                          <th>ID</th>\r\n                          <th>Type</th>\r\n                          <th>Condition</th>\r\n                          <th>Length (m)</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody>\r\n                        {defectData.image.kerbs.map((kerb, index) => (\r\n                          <tr key={index}>\r\n                            <td>{kerb.kerb_id}</td>\r\n                            <td>{kerb.kerb_type}</td>\r\n                            <td>\r\n                              <Badge \r\n                                bg={\r\n                                  kerb.condition === 'Good' ? 'success' :\r\n                                  kerb.condition === 'Fair' ? 'warning' : 'danger'\r\n                                }\r\n                                text={kerb.condition === 'Fair' ? 'dark' : undefined}\r\n                              >\r\n                                {kerb.condition}\r\n                              </Badge>\r\n                            </td>\r\n                            <td>{kerb.length_m?.toFixed(2) || 'N/A'}</td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                  \r\n                  {defectData.image.condition_counts && (\r\n                    <div className=\"mt-3\">\r\n                      <h6>Condition Distribution</h6>\r\n                      <div className=\"d-flex flex-wrap\">\r\n                        {Object.entries(defectData.image.condition_counts).map(([condition, count]) => (\r\n                          <div key={condition} className=\"me-3 mb-2\">\r\n                            <Badge \r\n                              bg={\r\n                                condition === 'Good' ? 'success' :\r\n                                condition === 'Fair' ? 'warning' : 'danger'\r\n                              }\r\n                              text={condition === 'Fair' ? 'dark' : undefined}\r\n                              className=\"me-1\"\r\n                            >\r\n                              {count}\r\n                            </Badge> {condition}\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Recommendation section - if available */}\r\n              <div className=\"mt-4\">\r\n                <Card className=\"bg-light\">\r\n                  <Card.Header>\r\n                    <h5 className=\"mb-0\">Recommended Action</h5>\r\n                  </Card.Header>\r\n                  <Card.Body>\r\n                    <p>Based on the defect analysis, the following action is recommended:</p>\r\n                    {defectData.type === 'pothole' && (\r\n                      <div>\r\n                        <ul>\r\n                          <li>Clean out loose material</li>\r\n                          <li>Apply tack coat</li>\r\n                          <li>Fill with hot mix asphalt</li>\r\n                          <li>Compact thoroughly</li>\r\n                        </ul>\r\n                        <p><strong>Priority:</strong> {\r\n                          defectData.image.potholes && defectData.image.potholes.length > 0 && \r\n                          defectData.image.potholes.some(p => p.area_cm2 > 1000) ? \r\n                          'High' : defectData.image.potholes && defectData.image.potholes.length > 0 && \r\n                          defectData.image.potholes.some(p => p.area_cm2 > 500) ? \r\n                          'Medium' : 'Low'\r\n                        }</p>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {defectData.type === 'crack' && (\r\n                      <div>\r\n                        <ul>\r\n                          <li>Clean cracks with compressed air</li>\r\n                          <li>Apply appropriate crack sealant</li>\r\n                          {defectData.image.type_counts && \r\n                           defectData.image.type_counts['Alligator Crack'] > 0 && (\r\n                            <li>Consider section replacement for alligator crack areas</li>\r\n                          )}\r\n                        </ul>\r\n                        <p><strong>Priority:</strong> {\r\n                          defectData.image.type_counts && \r\n                          defectData.image.type_counts['Alligator Crack'] > 0 ? \r\n                          'High' : 'Medium'\r\n                        }</p>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {defectData.type === 'kerb' && (\r\n                      <div>\r\n                        <ul>\r\n                          <li>Repair damaged sections</li>\r\n                          <li>Realign displaced kerbs</li>\r\n                          <li>Replace severely damaged kerbs</li>\r\n                        </ul>\r\n                        <p><strong>Priority:</strong> {\r\n                          defectData.image.condition_counts && \r\n                          defectData.image.condition_counts['Poor'] > 0 ? \r\n                          'High' : defectData.image.condition_counts['Fair'] > 0 ? \r\n                          'Medium' : 'Low'\r\n                        }</p>\r\n                      </div>\r\n                    )}\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n            </Card.Body>\r\n          </Card>\r\n\r\n          <div className=\"d-flex justify-content-end mt-4\">\r\n            <Button variant=\"primary\">\r\n              Generate Report\r\n            </Button>\r\n          </div>\r\n        </>\r\n      ) : (\r\n        <Alert variant=\"warning\">No defect data found for ID: {imageId}</Alert>\r\n      )}\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default DefectDetail; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAC1F,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAQ,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAC/B,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;;EAEzDC,SAAS,CAAC,MAAM;IACd,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFL,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMM,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAAC,wBAAwBX,OAAO,EAAE,CAAC;QAEnE,IAAIU,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBX,aAAa,CAACQ,QAAQ,CAACE,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLN,QAAQ,CAAC,+BAA+B,CAAC;QAC3C;QACAF,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOU,GAAG,EAAE;QACZC,OAAO,CAACV,KAAK,CAAC,gCAAgC,EAAES,GAAG,CAAC;QACpDR,QAAQ,CAAC,iCAAiCQ,GAAG,CAACE,OAAO,EAAE,CAAC;QACxDZ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIJ,OAAO,EAAE;MACXS,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACT,OAAO,CAAC,CAAC;EAEb,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC5BT,YAAY,CAACU,IAAI,IAAIA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG,UAAU,CAAC;EACtE,CAAC;EAED,MAAMC,kBAAkB,GAAIC,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,oBAAOzB,OAAA,CAACH,KAAK;UAAC6B,EAAE,EAAC,QAAQ;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAC3C,KAAK,OAAO;QACV,oBAAO/B,OAAA,CAACH,KAAK;UAAC6B,EAAE,EAAC,SAAS;UAACM,IAAI,EAAC,MAAM;UAAAL,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACtD,KAAK,MAAM;QACT,oBAAO/B,OAAA,CAACH,KAAK;UAAC6B,EAAE,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACzC;QACE,oBAAO/B,OAAA,CAACH,KAAK;UAAC6B,EAAE,EAAC,WAAW;UAAAC,QAAA,EAAEF;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;IAC/C;EACF,CAAC;EAED,MAAME,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,CAAC;EAC9C,CAAC;EAED,oBACEpC,OAAA,CAACV,SAAS;IAAC+C,SAAS,EAAC,MAAM;IAAAV,QAAA,gBACzB3B,OAAA;MAAKqC,SAAS,EAAC,wDAAwD;MAAAV,QAAA,gBACrE3B,OAAA;QAAA2B,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB/B,OAAA,CAACX,IAAI;QAACiD,EAAE,EAAC,YAAY;QAACD,SAAS,EAAC,yBAAyB;QAAAV,QAAA,EAAC;MAE1D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELvB,OAAO,gBACNR,OAAA;MAAKqC,SAAS,EAAC,kBAAkB;MAAAV,QAAA,eAC/B3B,OAAA,CAACL,OAAO;QAAC4C,SAAS,EAAC,QAAQ;QAACC,IAAI,EAAC,QAAQ;QAACC,OAAO,EAAC,SAAS;QAAAd,QAAA,eACzD3B,OAAA;UAAMqC,SAAS,EAAC,iBAAiB;UAAAV,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,GACJrB,KAAK,gBACPV,OAAA,CAACJ,KAAK;MAAC6C,OAAO,EAAC,QAAQ;MAAAd,QAAA,EAAEjB;IAAK;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,GACrCzB,UAAU,gBACZN,OAAA,CAAAE,SAAA;MAAAyB,QAAA,gBACE3B,OAAA,CAACP,IAAI;QAAC4C,SAAS,EAAC,gBAAgB;QAAAV,QAAA,gBAC9B3B,OAAA,CAACP,IAAI,CAACiD,MAAM;UAACL,SAAS,EAAC,uBAAuB;UAAAV,QAAA,eAC5C3B,OAAA;YAAKqC,SAAS,EAAC,mDAAmD;YAAAV,QAAA,gBAChE3B,OAAA;cAAIqC,SAAS,EAAC,MAAM;cAAAV,QAAA,GACjBH,kBAAkB,CAAClB,UAAU,CAACmB,IAAI,CAAC,EAAC,SAAO,EAACpB,OAAO;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACL/B,OAAA;cAAA2B,QAAA,gBACE3B,OAAA,CAACN,MAAM;gBACL+C,OAAO,EAAE7B,SAAS,KAAK,UAAU,GAAG,OAAO,GAAG,eAAgB;gBAC9D+B,IAAI,EAAC,IAAI;gBACTN,SAAS,EAAC,MAAM;gBAChBO,OAAO,EAAEtB,eAAgB;gBAAAK,QAAA,EAC1B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/B,OAAA,CAACN,MAAM;gBACL+C,OAAO,EAAE7B,SAAS,KAAK,WAAW,GAAG,OAAO,GAAG,eAAgB;gBAC/D+B,IAAI,EAAC,IAAI;gBACTC,OAAO,EAAEtB,eAAgB;gBAAAK,QAAA,EAC1B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACd/B,OAAA,CAACP,IAAI,CAACoD,IAAI;UAAAlB,QAAA,gBACR3B,OAAA,CAACT,GAAG;YAAAoC,QAAA,gBACF3B,OAAA,CAACR,GAAG;cAACsD,EAAE,EAAE,CAAE;cAACT,SAAS,EAAC,kBAAkB;cAAAV,QAAA,EACrCrB,UAAU,CAACyC,KAAK,iBACf/C,OAAA;gBAAKqC,SAAS,EAAC,wBAAwB;gBAAAV,QAAA,eACrC3B,OAAA;kBACEgD,GAAG,EAAE,2BACHpC,SAAS,KAAK,UAAU,GACpBN,UAAU,CAACyC,KAAK,CAACE,iBAAiB,GAClC3C,UAAU,CAACyC,KAAK,CAACG,kBAAkB,EACtC;kBACHC,GAAG,EAAE,GAAG7C,UAAU,CAACmB,IAAI,SAAU;kBACjCY,SAAS,EAAC,oCAAoC;kBAC9Ce,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAQ;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN/B,OAAA,CAACR,GAAG;cAACsD,EAAE,EAAE,CAAE;cAAAnB,QAAA,gBACT3B,OAAA;gBAAA2B,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B/B,OAAA;gBAAOqC,SAAS,EAAC,sBAAsB;gBAAAV,QAAA,eACrC3B,OAAA;kBAAA2B,QAAA,gBACE3B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAIsD,KAAK,EAAC,KAAK;sBAAA3B,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzB/B,OAAA;sBAAA2B,QAAA,EAAKrB,UAAU,CAACmB;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACL/B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrB/B,OAAA;sBAAA2B,QAAA,EACGrB,UAAU,CAACyC,KAAK,CAACQ,aAAa,IAC9BjD,UAAU,CAACyC,KAAK,CAACS,WAAW,IAC5BlD,UAAU,CAACyC,KAAK,CAACU,UAAU,IAAI;oBAAK;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACL/B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtB/B,OAAA;sBAAA2B,QAAA,EAAKM,UAAU,CAAC3B,UAAU,CAACyC,KAAK,CAACW,SAAS;oBAAC;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACL/B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpB/B,OAAA;sBAAA2B,QAAA,EAAKrB,UAAU,CAACyC,KAAK,CAACY,QAAQ,IAAI;oBAAK;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACL/B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACb/B,OAAA;sBAAA2B,QAAA,EAAKrB,UAAU,CAACyC,KAAK,CAACP,IAAI,IAAI;oBAAK;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACL/B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpB/B,OAAA;sBAAA2B,QAAA,EAAKrB,UAAU,CAACyC,KAAK,CAACa,WAAW,IAAI;oBAAe;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELzB,UAAU,CAACmB,IAAI,KAAK,SAAS,IAAInB,UAAU,CAACyC,KAAK,CAACc,QAAQ,iBACzD7D,OAAA;YAAKqC,SAAS,EAAC,MAAM;YAAAV,QAAA,gBACnB3B,OAAA;cAAA2B,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxB/B,OAAA;cAAKqC,SAAS,EAAC,kBAAkB;cAAAV,QAAA,eAC/B3B,OAAA;gBAAOqC,SAAS,EAAC,oCAAoC;gBAAAV,QAAA,gBACnD3B,OAAA;kBAAOqC,SAAS,EAAC,eAAe;kBAAAV,QAAA,eAC9B3B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACX/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnB/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnB/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpB/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvB/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrB/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR/B,OAAA;kBAAA2B,QAAA,EACGrB,UAAU,CAACyC,KAAK,CAACc,QAAQ,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;oBAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,eAAA;oBAAA,oBAC5CtE,OAAA;sBAAA2B,QAAA,gBACE3B,OAAA;wBAAA2B,QAAA,EAAKoC,OAAO,CAACQ;sBAAU;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7B/B,OAAA;wBAAA2B,QAAA,EAAK,EAAAsC,gBAAA,GAAAF,OAAO,CAACS,QAAQ,cAAAP,gBAAA,uBAAhBA,gBAAA,CAAkBQ,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChD/B,OAAA;wBAAA2B,QAAA,EAAK,EAAAuC,iBAAA,GAAAH,OAAO,CAACW,QAAQ,cAAAR,iBAAA,uBAAhBA,iBAAA,CAAkBO,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAChD/B,OAAA;wBAAA2B,QAAA,EAAK,EAAAwC,kBAAA,GAAAJ,OAAO,CAACY,SAAS,cAAAR,kBAAA,uBAAjBA,kBAAA,CAAmBM,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACjD/B,OAAA;wBAAA2B,QAAA,EAAK,EAAAyC,qBAAA,GAAAL,OAAO,CAACa,YAAY,cAAAR,qBAAA,uBAApBA,qBAAA,CAAsBK,OAAO,CAAC,CAAC,CAAC,OAAAJ,iBAAA,GAAIN,OAAO,CAACc,QAAQ,cAAAR,iBAAA,uBAAhBA,iBAAA,CAAkBI,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpF/B,OAAA;wBAAA2B,QAAA,EAAK,EAAA2C,eAAA,GAAAP,OAAO,CAACe,MAAM,cAAAR,eAAA,uBAAdA,eAAA,CAAgBG,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC9C/B,OAAA;wBAAA2B,QAAA,EACGoC,OAAO,CAACS,QAAQ,GAAG,IAAI,gBACtBxE,OAAA,CAACH,KAAK;0BAAC6B,EAAE,EAAC,QAAQ;0BAAAC,QAAA,EAAC;wBAAI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,GAC7BgC,OAAO,CAACS,QAAQ,GAAG,GAAG,gBACxBxE,OAAA,CAACH,KAAK;0BAAC6B,EAAE,EAAC,SAAS;0BAACM,IAAI,EAAC,MAAM;0BAAAL,QAAA,EAAC;wBAAM;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,gBAE9C/B,OAAA,CAACH,KAAK;0BAAC6B,EAAE,EAAC,SAAS;0BAAAC,QAAA,EAAC;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAC/B;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA,GAfEiC,KAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgBV,CAAC;kBAAA,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAzB,UAAU,CAACmB,IAAI,KAAK,OAAO,IAAInB,UAAU,CAACyC,KAAK,CAACgC,MAAM,iBACrD/E,OAAA;YAAKqC,SAAS,EAAC,MAAM;YAAAV,QAAA,gBACnB3B,OAAA;cAAA2B,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtB/B,OAAA;cAAKqC,SAAS,EAAC,kBAAkB;cAAAV,QAAA,eAC/B3B,OAAA;gBAAOqC,SAAS,EAAC,oCAAoC;gBAAAV,QAAA,gBACnD3B,OAAA;kBAAOqC,SAAS,EAAC,eAAe;kBAAAV,QAAA,eAC9B3B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACX/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACb/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnB/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR/B,OAAA;kBAAA2B,QAAA,EACGrB,UAAU,CAACyC,KAAK,CAACgC,MAAM,CAACjB,GAAG,CAAC,CAACkB,KAAK,EAAEhB,KAAK;oBAAA,IAAAiB,cAAA;oBAAA,oBACxCjF,OAAA;sBAAA2B,QAAA,gBACE3B,OAAA;wBAAA2B,QAAA,EAAKqD,KAAK,CAACE;sBAAQ;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzB/B,OAAA;wBAAA2B,QAAA,EAAKqD,KAAK,CAACG;sBAAU;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3B/B,OAAA;wBAAA2B,QAAA,EAAK,EAAAsD,cAAA,GAAAD,KAAK,CAACR,QAAQ,cAAAS,cAAA,uBAAdA,cAAA,CAAgBR,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC9C/B,OAAA;wBAAA2B,QAAA,GAAK,CAACqD,KAAK,CAACI,UAAU,GAAG,GAAG,EAAEX,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA,GAJxCiC,KAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKV,CAAC;kBAAA,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EAELzB,UAAU,CAACyC,KAAK,CAACsC,WAAW,iBAC3BrF,OAAA;cAAKqC,SAAS,EAAC,MAAM;cAAAV,QAAA,gBACnB3B,OAAA;gBAAA2B,QAAA,EAAI;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChC/B,OAAA;gBAAKqC,SAAS,EAAC,kBAAkB;gBAAAV,QAAA,EAC9B2D,MAAM,CAACC,OAAO,CAACjF,UAAU,CAACyC,KAAK,CAACsC,WAAW,CAAC,CAACvB,GAAG,CAAC,CAAC,CAACrC,IAAI,EAAE+D,KAAK,CAAC,kBAC9DxF,OAAA;kBAAgBqC,SAAS,EAAC,WAAW;kBAAAV,QAAA,gBACnC3B,OAAA,CAACH,KAAK;oBAAC6B,EAAE,EAAC,MAAM;oBAACW,SAAS,EAAC,MAAM;oBAAAV,QAAA,EAAE6D;kBAAK;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACN,IAAI;gBAAA,GAD/CA,IAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAEAzB,UAAU,CAACmB,IAAI,KAAK,MAAM,IAAInB,UAAU,CAACyC,KAAK,CAAC0C,KAAK,iBACnDzF,OAAA;YAAKqC,SAAS,EAAC,MAAM;YAAAV,QAAA,gBACnB3B,OAAA;cAAA2B,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB/B,OAAA;cAAKqC,SAAS,EAAC,kBAAkB;cAAAV,QAAA,eAC/B3B,OAAA;gBAAOqC,SAAS,EAAC,oCAAoC;gBAAAV,QAAA,gBACnD3B,OAAA;kBAAOqC,SAAS,EAAC,eAAe;kBAAAV,QAAA,eAC9B3B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACX/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACb/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClB/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR/B,OAAA;kBAAA2B,QAAA,EACGrB,UAAU,CAACyC,KAAK,CAAC0C,KAAK,CAAC3B,GAAG,CAAC,CAAC4B,IAAI,EAAE1B,KAAK;oBAAA,IAAA2B,cAAA;oBAAA,oBACtC3F,OAAA;sBAAA2B,QAAA,gBACE3B,OAAA;wBAAA2B,QAAA,EAAK+D,IAAI,CAACE;sBAAO;wBAAAhE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACvB/B,OAAA;wBAAA2B,QAAA,EAAK+D,IAAI,CAACG;sBAAS;wBAAAjE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACzB/B,OAAA;wBAAA2B,QAAA,eACE3B,OAAA,CAACH,KAAK;0BACJ6B,EAAE,EACAgE,IAAI,CAACI,SAAS,KAAK,MAAM,GAAG,SAAS,GACrCJ,IAAI,CAACI,SAAS,KAAK,MAAM,GAAG,SAAS,GAAG,QACzC;0BACD9D,IAAI,EAAE0D,IAAI,CAACI,SAAS,KAAK,MAAM,GAAG,MAAM,GAAGC,SAAU;0BAAApE,QAAA,EAEpD+D,IAAI,CAACI;wBAAS;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACL/B,OAAA;wBAAA2B,QAAA,EAAK,EAAAgE,cAAA,GAAAD,IAAI,CAACM,QAAQ,cAAAL,cAAA,uBAAbA,cAAA,CAAelB,OAAO,CAAC,CAAC,CAAC,KAAI;sBAAK;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA,GAdtCiC,KAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAeV,CAAC;kBAAA,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EAELzB,UAAU,CAACyC,KAAK,CAACkD,gBAAgB,iBAChCjG,OAAA;cAAKqC,SAAS,EAAC,MAAM;cAAAV,QAAA,gBACnB3B,OAAA;gBAAA2B,QAAA,EAAI;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/B/B,OAAA;gBAAKqC,SAAS,EAAC,kBAAkB;gBAAAV,QAAA,EAC9B2D,MAAM,CAACC,OAAO,CAACjF,UAAU,CAACyC,KAAK,CAACkD,gBAAgB,CAAC,CAACnC,GAAG,CAAC,CAAC,CAACgC,SAAS,EAAEN,KAAK,CAAC,kBACxExF,OAAA;kBAAqBqC,SAAS,EAAC,WAAW;kBAAAV,QAAA,gBACxC3B,OAAA,CAACH,KAAK;oBACJ6B,EAAE,EACAoE,SAAS,KAAK,MAAM,GAAG,SAAS,GAChCA,SAAS,KAAK,MAAM,GAAG,SAAS,GAAG,QACpC;oBACD9D,IAAI,EAAE8D,SAAS,KAAK,MAAM,GAAG,MAAM,GAAGC,SAAU;oBAChD1D,SAAS,EAAC,MAAM;oBAAAV,QAAA,EAEf6D;kBAAK;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,KAAC,EAAC+D,SAAS;gBAAA,GAVXA,SAAS;kBAAAlE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWd,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,eAGD/B,OAAA;YAAKqC,SAAS,EAAC,MAAM;YAAAV,QAAA,eACnB3B,OAAA,CAACP,IAAI;cAAC4C,SAAS,EAAC,UAAU;cAAAV,QAAA,gBACxB3B,OAAA,CAACP,IAAI,CAACiD,MAAM;gBAAAf,QAAA,eACV3B,OAAA;kBAAIqC,SAAS,EAAC,MAAM;kBAAAV,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACd/B,OAAA,CAACP,IAAI,CAACoD,IAAI;gBAAAlB,QAAA,gBACR3B,OAAA;kBAAA2B,QAAA,EAAG;gBAAkE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACxEzB,UAAU,CAACmB,IAAI,KAAK,SAAS,iBAC5BzB,OAAA;kBAAA2B,QAAA,gBACE3B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjC/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxB/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClC/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACL/B,OAAA;oBAAA2B,QAAA,gBAAG3B,OAAA;sBAAA2B,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAC5BzB,UAAU,CAACyC,KAAK,CAACc,QAAQ,IAAIvD,UAAU,CAACyC,KAAK,CAACc,QAAQ,CAACqC,MAAM,GAAG,CAAC,IACjE5F,UAAU,CAACyC,KAAK,CAACc,QAAQ,CAACsC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,QAAQ,GAAG,IAAI,CAAC,GACtD,MAAM,GAAGlE,UAAU,CAACyC,KAAK,CAACc,QAAQ,IAAIvD,UAAU,CAACyC,KAAK,CAACc,QAAQ,CAACqC,MAAM,GAAG,CAAC,IAC1E5F,UAAU,CAACyC,KAAK,CAACc,QAAQ,CAACsC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,QAAQ,GAAG,GAAG,CAAC,GACrD,QAAQ,GAAG,KAAK;kBAAA;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,EAEAzB,UAAU,CAACmB,IAAI,KAAK,OAAO,iBAC1BzB,OAAA;kBAAA2B,QAAA,gBACE3B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAgC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzC/B,OAAA;sBAAA2B,QAAA,EAAI;oBAA+B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACvCzB,UAAU,CAACyC,KAAK,CAACsC,WAAW,IAC5B/E,UAAU,CAACyC,KAAK,CAACsC,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,iBAClDrF,OAAA;sBAAA2B,QAAA,EAAI;oBAAsD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAC/D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACL/B,OAAA;oBAAA2B,QAAA,gBAAG3B,OAAA;sBAAA2B,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAC5BzB,UAAU,CAACyC,KAAK,CAACsC,WAAW,IAC5B/E,UAAU,CAACyC,KAAK,CAACsC,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,GACnD,MAAM,GAAG,QAAQ;kBAAA;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN,EAEAzB,UAAU,CAACmB,IAAI,KAAK,MAAM,iBACzBzB,OAAA;kBAAA2B,QAAA,gBACE3B,OAAA;oBAAA2B,QAAA,gBACE3B,OAAA;sBAAA2B,QAAA,EAAI;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChC/B,OAAA;sBAAA2B,QAAA,EAAI;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChC/B,OAAA;sBAAA2B,QAAA,EAAI;oBAA8B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACL/B,OAAA;oBAAA2B,QAAA,gBAAG3B,OAAA;sBAAA2B,QAAA,EAAQ;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAC5BzB,UAAU,CAACyC,KAAK,CAACkD,gBAAgB,IACjC3F,UAAU,CAACyC,KAAK,CAACkD,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,GAC7C,MAAM,GAAG3F,UAAU,CAACyC,KAAK,CAACkD,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,GACtD,QAAQ,GAAG,KAAK;kBAAA;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP/B,OAAA;QAAKqC,SAAS,EAAC,iCAAiC;QAAAV,QAAA,eAC9C3B,OAAA,CAACN,MAAM;UAAC+C,OAAO,EAAC,SAAS;UAAAd,QAAA,EAAC;QAE1B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CAAC,gBAEH/B,OAAA,CAACJ,KAAK;MAAC6C,OAAO,EAAC,SAAS;MAAAd,QAAA,GAAC,+BAA6B,EAACtB,OAAO;IAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACvE;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB;AAAC3B,EAAA,CAnXQD,YAAY;EAAA,QACCf,SAAS;AAAA;AAAAiH,EAAA,GADtBlG,YAAY;AAqXrB,eAAeA,YAAY;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}